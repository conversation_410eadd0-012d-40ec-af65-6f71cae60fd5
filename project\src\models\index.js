
// User Schema
const mongoose = require('mongoose');

const UserSchema = new mongoose.Schema({
  username: { type: String, lowercase: true, required: true, unique: true },
  email: { type: String, required: true, unique: true },
  passwordHash: { type: String, required: true },
  googleId: { type: String, unique: true, sparse: true }, // For Google Sign-In users
  photoUrl: { type: String }, // Profile photo URL
  location: {
    type: {
      type: String,
      enum: ['Point'],
      default: 'Point'
    },
    coordinates: {
      type: [Number], // [longitude, latitude]
      default: [0, 0]
    }
  },
  moodEntries: [
    {
      date: { type: Date, required: true },
      mood: { type: String, required: true },
      journalEntryId: { type: mongoose.Schema.Types.ObjectId, ref: 'JournalEntry' },
    },
  ],
  recommendations: [
    {
      recommendationId: { type: mongoose.Schema.Types.ObjectId, required: true },
      recommendationText: { type: String, required: true },
      date: { type: Date, required: true },
    },
  ],
  therapistId: { type: mongoose.Schema.Types.ObjectId, ref: 'Therapist' },
  paymentIds: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Payment' }],
  role: { type: String, enum: ['user', 'therapist', 'admin'], required: true },
});

// Create 2dsphere index for geospatial queries
UserSchema.index({ location: "2dsphere" });



// Journal Entry Schema
const JournalEntrySchema = new mongoose.Schema({
  userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  entryDate: { type: Date, required: true },
  content: { type: String, required: true },
  sentimentScore: { type: Number, required: true },
});

// Payment Schema
const PaymentSchema = new mongoose.Schema({
  userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  amount: { type: Number, required: true },
  paymentDate: { type: Date, required: true },
  transactionStatus: { type: String, enum: ['Pending', 'Completed', 'Failed'], required: true },
});

// Therapist Schema
const TherapistSchema = new mongoose.Schema({
  name: { type: String, required: true },
  specialty: { type: String, required: true },
  location: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  passwordHash: { type: String, required: true },
  userIds: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],
});

const moodSchema = new mongoose.Schema({
  userId: { type: String, required: true },
  mood: { type: String, required: true },
  note: { type: String },
  date: { type: Date, default: Date.now },
});

const AppointmentSchema = new mongoose.Schema({
  userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  therapistId: { type: mongoose.Schema.Types.ObjectId, ref: 'Therapist', required: true },
  appointmentDate: { type: Date, required: true },
  duration: { type: Number, required: true }, // Duration in minutes
  status: { 
    type: String, 
    enum: ['scheduled', 'completed', 'cancelled', 'rescheduled'], 
    default: 'scheduled',
    required: true 
  },
  notes: { type: String },
  meetingLink: { type: String },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Export Models
module.exports = {
  User: mongoose.model('User', UserSchema),
  JournalEntry: mongoose.model('JournalEntry', JournalEntrySchema),
  Payment: mongoose.model('Payment', PaymentSchema),
  Therapist: mongoose.model('Therapist', TherapistSchema),
  Mood: mongoose.model('Mood', moodSchema),
  Appointment: mongoose.model('Appointment', AppointmentSchema),
};
