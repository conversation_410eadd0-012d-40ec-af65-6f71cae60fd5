import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:myapp/providers/theme_provider.dart';

class AnxietyDepressionTestScreen extends StatefulWidget {
  final String userId;

  const AnxietyDepressionTestScreen({super.key, required this.userId});

  @override
  _AnxietyDepressionTestScreenState createState() =>
      _AnxietyDepressionTestScreenState();
}

class _AnxietyDepressionTestScreenState
    extends State<AnxietyDepressionTestScreen> {
  int currentQuestionIndex = 0;
  List<int> answers = [];
  bool isLoading = false;
  bool testCompleted = false;
  Map<String, dynamic>? testResult;

  final List<Map<String, dynamic>> questions = [
    {
      'question':
          'Over the last 2 weeks, how often have you been bothered by feeling down, depressed, or hopeless?',
      'type': 'depression'
    },
    {
      'question':
          'Over the last 2 weeks, how often have you been bothered by little interest or pleasure in doing things?',
      'type': 'depression'
    },
    {
      'question':
          'Over the last 2 weeks, how often have you been bothered by feeling nervous, anxious, or on edge?',
      'type': 'anxiety'
    },
    {
      'question':
          'Over the last 2 weeks, how often have you been bothered by not being able to stop or control worrying?',
      'type': 'anxiety'
    },
    {
      'question':
          'Over the last 2 weeks, how often have you been bothered by trouble falling or staying asleep?',
      'type': 'both'
    },
    {
      'question':
          'Over the last 2 weeks, how often have you been bothered by feeling tired or having little energy?',
      'type': 'both'
    },
    {
      'question':
          'Over the last 2 weeks, how often have you been bothered by poor appetite or overeating?',
      'type': 'depression'
    },
    {
      'question':
          'Over the last 2 weeks, how often have you been bothered by worrying too much about different things?',
      'type': 'anxiety'
    },
    {
      'question':
          'Over the last 2 weeks, how often have you been bothered by trouble relaxing?',
      'type': 'anxiety'
    },
    {
      'question':
          'Over the last 2 weeks, how often have you been bothered by feeling bad about yourself?',
      'type': 'depression'
    },
  ];

  final List<String> answerOptions = [
    'Not at all',
    'Several days',
    'More than half the days',
    'Nearly every day'
  ];

  @override
  void initState() {
    super.initState();
    answers = List.filled(questions.length, -1);
  }

  void _selectAnswer(int answerIndex) {
    setState(() {
      answers[currentQuestionIndex] = answerIndex;
    });
  }

  void _nextQuestion() {
    if (currentQuestionIndex < questions.length - 1) {
      setState(() {
        currentQuestionIndex++;
      });
    } else {
      _submitTest();
    }
  }

  void _previousQuestion() {
    if (currentQuestionIndex > 0) {
      setState(() {
        currentQuestionIndex--;
      });
    }
  }

  Future<void> _submitTest() async {
    setState(() {
      isLoading = true;
    });

    try {
      // Calculate scores
      int depressionScore = 0;
      int anxietyScore = 0;

      for (int i = 0; i < questions.length; i++) {
        final questionType = questions[i]['type'];
        final score = answers[i];

        if (questionType == 'depression' || questionType == 'both') {
          depressionScore += score;
        }
        if (questionType == 'anxiety' || questionType == 'both') {
          anxietyScore += score;
        }
      }

      final response = await http.post(
        Uri.parse('http://localhost:3000/api/mental-health-assessment'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'userId': widget.userId,
          'answers': answers,
          'depressionScore': depressionScore,
          'anxietyScore': anxietyScore,
          'testDate': DateTime.now().toIso8601String(),
        }),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final result = jsonDecode(response.body);
        setState(() {
          testResult = {
            'depressionScore': depressionScore,
            'anxietyScore': anxietyScore,
            'depressionLevel': _getScoreLevel(depressionScore, 'depression'),
            'anxietyLevel': _getScoreLevel(anxietyScore, 'anxiety'),
            'recommendations':
                _getRecommendations(depressionScore, anxietyScore),
          };
          testCompleted = true;
          isLoading = false;
        });
      } else {
        throw Exception('Failed to submit test');
      }
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error submitting test: $e')),
      );
    }
  }

  String _getScoreLevel(int score, String type) {
    if (score <= 4) return 'Minimal';
    if (score <= 9) return 'Mild';
    if (score <= 14) return 'Moderate';
    if (score <= 19) return 'Moderately Severe';
    return 'Severe';
  }

  List<String> _getRecommendations(int depressionScore, int anxietyScore) {
    List<String> recommendations = [];

    if (depressionScore > 9 || anxietyScore > 9) {
      recommendations
          .add('Consider speaking with a mental health professional');
      recommendations.add(
          'Practice regular exercise and maintain a healthy sleep schedule');
    }

    if (depressionScore > 14 || anxietyScore > 14) {
      recommendations.add('Seek immediate professional help');
      recommendations.add('Consider therapy or counseling services');
    }

    if (recommendations.isEmpty) {
      recommendations.add('Continue maintaining good mental health practices');
      recommendations.add('Regular check-ins with yourself are beneficial');
    }

    return recommendations;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundLight,
      appBar: AppBar(
        title: const Text(
          'Mental Health Assessment',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            letterSpacing: -0.5,
          ),
        ),
        backgroundColor: AppTheme.surfaceWhite,
        foregroundColor: AppTheme.textDark,
        elevation: 0,
        shadowColor: Colors.transparent,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.primaryGradient,
        ),
        child: SafeArea(
          child: isLoading
              ? const Center(
                  child: CircularProgressIndicator(color: Colors.white))
              : testCompleted
                  ? _buildResultScreen()
                  : _buildQuestionScreen(),
        ),
      ),
    );
  }

  Widget _buildQuestionScreen() {
    final progress = (currentQuestionIndex + 1) / questions.length;

    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Progress indicator
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.grey[300],
            valueColor: const AlwaysStoppedAnimation<Color>(Colors.purple),
          ),
          const SizedBox(height: 10),
          Text(
            'Question ${currentQuestionIndex + 1} of ${questions.length}',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
          ),
          const SizedBox(height: 30),

          // Question
          Text(
            questions[currentQuestionIndex]['question'],
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
          ),
          const SizedBox(height: 30),

          // Answer options
          Expanded(
            child: ListView.builder(
              itemCount: answerOptions.length,
              itemBuilder: (context, index) {
                final isSelected = answers[currentQuestionIndex] == index;
                return Card(
                  margin: const EdgeInsets.only(bottom: 12),
                  child: ListTile(
                    title: Text(answerOptions[index]),
                    leading: Radio<int>(
                      value: index,
                      groupValue: answers[currentQuestionIndex],
                      onChanged: (value) => _selectAnswer(value!),
                      activeColor: Colors.purple,
                    ),
                    onTap: () => _selectAnswer(index),
                    tileColor:
                        isSelected ? Colors.purple.withOpacity(0.1) : null,
                  ),
                );
              },
            ),
          ),

          // Navigation buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if (currentQuestionIndex > 0)
                ElevatedButton(
                  onPressed: _previousQuestion,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey,
                  ),
                  child: const Text('Previous'),
                ),
              const Spacer(),
              ElevatedButton(
                onPressed:
                    answers[currentQuestionIndex] != -1 ? _nextQuestion : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple,
                ),
                child: Text(
                  currentQuestionIndex == questions.length - 1
                      ? 'Submit'
                      : 'Next',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildResultScreen() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Icon(
            Icons.check_circle,
            color: Colors.green,
            size: 64,
          ),
          const SizedBox(height: 20),
          Text(
            'Assessment Complete',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 30),

          // Results
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Your Results',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 16),
                  _buildScoreRow('Depression', testResult!['depressionLevel'],
                      Colors.blue),
                  const SizedBox(height: 12),
                  _buildScoreRow(
                      'Anxiety', testResult!['anxietyLevel'], Colors.orange),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),

          // Recommendations
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Recommendations',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 16),
                  ...testResult!['recommendations']
                      .map<Widget>(
                        (rec) => Padding(
                          padding: const EdgeInsets.only(bottom: 8),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Icon(Icons.check,
                                  color: Colors.green, size: 20),
                              const SizedBox(width: 8),
                              Expanded(child: Text(rec)),
                            ],
                          ),
                        ),
                      )
                      .toList(),
                ],
              ),
            ),
          ),
          const Spacer(),

          // Action buttons
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Back to Home'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    // Navigate to booking screen
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                  ),
                  child: const Text('Book Session'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildScoreRow(String label, String level, Color color) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.titleMedium,
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: color),
          ),
          child: Text(
            level,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }
}
