!function(t){function h(t,s){return this instanceof h?(this._low=0,this._high=0,this.remainder=null,"undefined"==typeof s?o.call(this,t):"string"==typeof t?r.call(this,t,s):void i.call(this,t,s)):new h(t,s)}function i(t,h){return this._low=0|t,this._high=0|h,this}function o(t){return this._low=65535&t,this._high=t>>>16,this}function r(t,h){var i=parseInt(t,h||10);return this._low=65535&i,this._high=i>>>16,this}({36:h(Math.pow(36,5)),16:h(Math.pow(16,7)),10:h(Math.pow(10,9)),2:h(Math.pow(2,30))}),{36:h(36),16:h(16),10:h(10),2:h(2)};h.prototype.fromBits=i,h.prototype.fromNumber=o,h.prototype.fromString=r,h.prototype.toNumber=function(){return 65536*this._high+this._low},h.prototype.toString=function(t){return this.toNumber().toString(t||10)},h.prototype.add=function(t){var h=this._low+t._low,i=h>>>16;return i+=this._high+t._high,this._low=65535&h,this._high=65535&i,this},h.prototype.subtract=function(t){return this.add(t.clone().negate())},h.prototype.multiply=function(t){var h,i,o=this._high,r=this._low,s=t._high,e=t._low;return i=r*e,h=i>>>16,h+=o*e,h&=65535,h+=r*s,this._low=65535&i,this._high=65535&h,this},h.prototype.div=function(t){if(0==t._low&&0==t._high)throw Error("division by zero");if(0==t._high&&1==t._low)return this.remainder=new h(0),this;if(t.gt(this))return this.remainder=this.clone(),this._low=0,this._high=0,this;if(this.eq(t))return this.remainder=new h(0),this._low=1,this._high=0,this;for(var i=t.clone(),o=-1;!this.lt(i);)i.shiftLeft(1,!0),o++;for(this.remainder=this.clone(),this._low=0,this._high=0;o>=0;o--)i.shiftRight(1),this.remainder.lt(i)||(this.remainder.subtract(i),o>=16?this._high|=1<<o-16:this._low|=1<<o);return this},h.prototype.negate=function(){var t=(65535&~this._low)+1;return this._low=65535&t,this._high=~this._high+(t>>>16)&65535,this},h.prototype.equals=h.prototype.eq=function(t){return this._low==t._low&&this._high==t._high},h.prototype.greaterThan=h.prototype.gt=function(t){return this._high>t._high?!0:this._high<t._high?!1:this._low>t._low},h.prototype.lessThan=h.prototype.lt=function(t){return this._high<t._high?!0:this._high>t._high?!1:this._low<t._low},h.prototype.or=function(t){return this._low|=t._low,this._high|=t._high,this},h.prototype.and=function(t){return this._low&=t._low,this._high&=t._high,this},h.prototype.not=function(){return this._low=65535&~this._low,this._high=65535&~this._high,this},h.prototype.xor=function(t){return this._low^=t._low,this._high^=t._high,this},h.prototype.shiftRight=h.prototype.shiftr=function(t){return t>16?(this._low=this._high>>t-16,this._high=0):16==t?(this._low=this._high,this._high=0):(this._low=this._low>>t|this._high<<16-t&65535,this._high>>=t),this},h.prototype.shiftLeft=h.prototype.shiftl=function(t,h){return t>16?(this._high=this._low<<t-16,this._low=0,h||(this._high&=65535)):16==t?(this._high=this._low,this._low=0):(this._high=this._high<<t|this._low>>16-t,this._low=this._low<<t&65535,h||(this._high&=65535)),this},h.prototype.rotateLeft=h.prototype.rotl=function(t){var h=this._high<<16|this._low;return h=h<<t|h>>>32-t,this._low=65535&h,this._high=h>>>16,this},h.prototype.rotateRight=h.prototype.rotr=function(t){var h=this._high<<16|this._low;return h=h>>>t|h<<32-t,this._low=65535&h,this._high=h>>>16,this},h.prototype.clone=function(){return new h(this._low,this._high)},"undefined"!=typeof define&&define.amd?define([],function(){return h}):"undefined"!=typeof module&&module.exports?module.exports=h:t.UINT32=h}(this);