const User = require('../models/User');

// Helper function to calculate distance between two points
const calculateDistance = (lat1, lon1, lat2, lon2) => {
  const R = 6371; // Radius of the Earth in kilometers
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a =
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c; // Distance in kilometers
};

exports.updateLocation = async (req, res) => {
  const { userId, latitude, longitude, address } = req.body;

  // Validate required fields presence
  if (!userId) {
    return res.status(400).json({ error: 'User ID is required' });
  }

  // Validate latitude and longitude types and ranges
  if (
    typeof latitude !== 'number' ||
    typeof longitude !== 'number' ||
    latitude < -90 || latitude > 90 ||
    longitude < -180 || longitude > 180
  ) {
    return res.status(400).json({ error: 'Invalid latitude or longitude' });
  }

  try {
    // Find user by ID and update location
    const user = await User.findByIdAndUpdate(
      userId,
      {
        location: {
          type: 'Point',
          coordinates: [longitude, latitude],
        },
        address: address || null,
        lastLocationUpdate: new Date(),
      },
      { new: true } // Return updated user document
    );

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Return success with updated location
    return res.status(200).json({
      message: 'Location updated successfully',
      location: user.location,
      address: user.address,
    });
  } catch (error) {
    console.error('Error updating location:', error);
    return res.status(500).json({ error: 'Failed to update location' });
  }
};

// Find nearby therapists
exports.findNearbyTherapists = async (req, res) => {
  const { lat, lng, radius = 10 } = req.query;

  // Validate coordinates
  if (!lat || !lng) {
    return res.status(400).json({ error: 'Latitude and longitude are required' });
  }

  const latitude = parseFloat(lat);
  const longitude = parseFloat(lng);
  const radiusKm = parseFloat(radius);

  if (isNaN(latitude) || isNaN(longitude) || isNaN(radiusKm)) {
    return res.status(400).json({ error: 'Invalid coordinates or radius' });
  }

  try {
    // Find therapists within radius using MongoDB geospatial query
    const therapists = await User.find({
      userType: 'therapist',
      location: {
        $near: {
          $geometry: {
            type: 'Point',
            coordinates: [longitude, latitude]
          },
          $maxDistance: radiusKm * 1000 // Convert km to meters
        }
      },
      isActive: true
    }).select('name specialty location rating reviewCount profileImage phone email description');

    // Calculate exact distances and add to response
    const therapistsWithDistance = therapists.map(therapist => {
      const distance = calculateDistance(
        latitude, longitude,
        therapist.location.coordinates[1], // lat
        therapist.location.coordinates[0]  // lng
      );

      return {
        ...therapist.toObject(),
        distance: Math.round(distance * 100) / 100, // Round to 2 decimal places
        location: {
          latitude: therapist.location.coordinates[1],
          longitude: therapist.location.coordinates[0]
        }
      };
    });

    // Sort by distance
    therapistsWithDistance.sort((a, b) => a.distance - b.distance);

    res.status(200).json(therapistsWithDistance);
  } catch (error) {
    console.error('Error finding nearby therapists:', error);
    res.status(500).json({ error: 'Failed to find nearby therapists' });
  }
};

// Get therapist location
exports.getTherapistLocation = async (req, res) => {
  const { therapistId } = req.params;

  try {
    const therapist = await User.findById(therapistId).select('location name');

    if (!therapist) {
      return res.status(404).json({ error: 'Therapist not found' });
    }

    if (!therapist.location) {
      return res.status(404).json({ error: 'Therapist location not available' });
    }

    res.status(200).json({
      name: therapist.name,
      latitude: therapist.location.coordinates[1],
      longitude: therapist.location.coordinates[0]
    });
  } catch (error) {
    console.error('Error getting therapist location:', error);
    res.status(500).json({ error: 'Failed to get therapist location' });
  }
};

// Get location-based recommendations
exports.getLocationBasedRecommendations = async (req, res) => {
  const { lat, lng } = req.query;

  if (!lat || !lng) {
    return res.status(400).json({ error: 'Latitude and longitude are required' });
  }

  const latitude = parseFloat(lat);
  const longitude = parseFloat(lng);

  try {
    // Find nearby mental health resources, support groups, etc.
    const recommendations = {
      nearbyTherapists: await User.countDocuments({
        userType: 'therapist',
        location: {
          $near: {
            $geometry: {
              type: 'Point',
              coordinates: [longitude, latitude]
            },
            $maxDistance: 25000 // 25km
          }
        },
        isActive: true
      }),
      emergencyContacts: [
        {
          name: 'National Suicide Prevention Lifeline',
          phone: '988',
          available: '24/7'
        },
        {
          name: 'Crisis Text Line',
          phone: 'Text HOME to 741741',
          available: '24/7'
        }
      ],
      localResources: [
        {
          name: 'Community Mental Health Center',
          type: 'clinic',
          distance: '2.5 km'
        },
        {
          name: 'Support Group Meetings',
          type: 'support_group',
          distance: '1.8 km'
        }
      ]
    };

    res.status(200).json(recommendations);
  } catch (error) {
    console.error('Error getting location-based recommendations:', error);
    res.status(500).json({ error: 'Failed to get recommendations' });
  }
};
