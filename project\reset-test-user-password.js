const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/mindease', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

// Define User schema (simplified)
const userSchema = new mongoose.Schema({
  username: String,
  email: String,
  passwordHash: String,
  role: String,
});

const User = mongoose.model('User', userSchema);

async function resetTestUserPassword() {
  try {
    console.log('🔄 Resetting test user password...');
    
    // Find the test user
    const user = await User.findOne({ email: '<EMAIL>' });
    
    if (!user) {
      console.log('❌ Test user not found');
      return;
    }
    
    console.log(`✅ Found user: ${user.username} (${user.email})`);
    
    // Hash the new password
    const newPassword = 'password123';
    const hashedPassword = await bcrypt.hash(newPassword, 10);
    
    // Update the user's password
    user.passwordHash = hashedPassword;
    await user.save();
    
    console.log('✅ Password reset successfully!');
    console.log(`📧 Email: ${user.email}`);
    console.log(`🔑 Password: ${newPassword}`);
    console.log(`👤 Role: ${user.role}`);
    
  } catch (error) {
    console.error('❌ Error resetting password:', error);
  } finally {
    mongoose.connection.close();
    console.log('🔌 Database connection closed');
  }
}

resetTestUserPassword();
