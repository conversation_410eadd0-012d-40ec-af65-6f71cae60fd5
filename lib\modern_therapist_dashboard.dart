import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:myapp/login_page.dart';
import 'package:myapp/api_service.dart';
import 'package:myapp/mock_api_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ModernTherapistDashboard extends StatefulWidget {
  final String therapistId;

  const ModernTherapistDashboard({super.key, required this.therapistId});

  @override
  State<ModernTherapistDashboard> createState() => _ModernTherapistDashboardState();
}

class _ModernTherapistDashboardState extends State<ModernTherapistDashboard>
    with TickerProviderStateMixin {
  int _selectedIndex = 0;
  final DateFormat _dateFormat = DateFormat('MMM dd, yyyy');
  final DateFormat _timeFormat = DateFormat('hh:mm a');
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  // Data variables
  Map<String, dynamic>? _therapistData;
  List<Map<String, dynamic>> _patients = [];
  List<Map<String, dynamic>> _appointments = [];
  List<Map<String, dynamic>> _analytics = [];
  bool _isLoading = true;
  String _error = '';

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _loadDashboardData();
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadDashboardData() async {
    setState(() {
      _isLoading = true;
      _error = '';
    });

    try {
      await _loadTherapistProfile();
      await _loadPatients();
      await _loadAppointments();
      await _loadAnalytics();
    } catch (e) {
      print('Error loading dashboard data: $e');
      _loadMockData();
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _loadTherapistProfile() async {
    try {
      final result = await ApiService.getUserById(widget.therapistId);
      if (result['success']) {
        _therapistData = result['data'];
      } else {
        throw Exception('Failed to load therapist profile');
      }
    } catch (e) {
      _therapistData = {
        'id': widget.therapistId,
        'name': 'Dr. Sarah Johnson',
        'email': '<EMAIL>',
        'specialization': 'Anxiety & Depression',
        'experience': '8 years',
        'rating': 4.9,
        'location': 'New York, NY',
        'avatar': 'https://via.placeholder.com/150',
      };
    }
  }

  Future<void> _loadPatients() async {
    try {
      final result = await ApiService.getUserAppointments(widget.therapistId);
      if (result['success']) {
        _patients = List<Map<String, dynamic>>.from(result['data'] ?? []);
      } else {
        throw Exception('Failed to load patients');
      }
    } catch (e) {
      _patients = [
        {
          'id': '1',
          'name': 'John Doe',
          'age': 28,
          'email': '<EMAIL>',
          'nextAppointment': DateTime.now().add(const Duration(days: 1)).toIso8601String(),
          'progress': 75,
          'status': 'Active',
          'lastSession': DateTime.now().subtract(const Duration(days: 7)).toIso8601String(),
          'avatar': 'https://via.placeholder.com/100',
        },
        {
          'id': '2',
          'name': 'Jane Smith',
          'age': 34,
          'email': '<EMAIL>',
          'nextAppointment': DateTime.now().add(const Duration(days: 3)).toIso8601String(),
          'progress': 60,
          'status': 'Active',
          'lastSession': DateTime.now().subtract(const Duration(days: 3)).toIso8601String(),
          'avatar': 'https://via.placeholder.com/100',
        },
        {
          'id': '3',
          'name': 'Mike Johnson',
          'age': 42,
          'email': '<EMAIL>',
          'nextAppointment': DateTime.now().add(const Duration(days: 5)).toIso8601String(),
          'progress': 85,
          'status': 'Active',
          'lastSession': DateTime.now().subtract(const Duration(days: 1)).toIso8601String(),
          'avatar': 'https://via.placeholder.com/100',
        },
      ];
    }
  }

  Future<void> _loadAppointments() async {
    try {
      final result = await ApiService.getUserAppointments(widget.therapistId);
      if (result['success']) {
        _appointments = List<Map<String, dynamic>>.from(result['data'] ?? []);
      } else {
        throw Exception('Failed to load appointments');
      }
    } catch (e) {
      _appointments = [
        {
          'id': '1',
          'patientName': 'John Doe',
          'patientId': '1',
          'time': '10:00 AM',
          'date': DateTime.now().toIso8601String(),
          'duration': 60,
          'type': 'Therapy Session',
          'status': 'Scheduled',
          'notes': 'Regular therapy session',
        },
        {
          'id': '2',
          'patientName': 'Jane Smith',
          'patientId': '2',
          'time': '2:00 PM',
          'date': DateTime.now().toIso8601String(),
          'duration': 45,
          'type': 'Follow-up',
          'status': 'Scheduled',
          'notes': 'Follow-up session',
        },
        {
          'id': '3',
          'patientName': 'Mike Johnson',
          'patientId': '3',
          'time': '4:00 PM',
          'date': DateTime.now().add(const Duration(days: 1)).toIso8601String(),
          'duration': 60,
          'type': 'Initial Consultation',
          'status': 'Scheduled',
          'notes': 'First session',
        },
      ];
    }
  }

  Future<void> _loadAnalytics() async {
    _analytics = [
      {
        'title': 'Total Patients',
        'value': _patients.length.toString(),
        'icon': Icons.people,
        'color': const Color(0xFF8B5CF6),
        'trend': '+12%',
        'subtitle': 'Active patients',
      },
      {
        'title': 'Today\'s Sessions',
        'value': _appointments.where((apt) {
          final aptDate = DateTime.parse(apt['date']);
          final today = DateTime.now();
          return aptDate.year == today.year &&
                 aptDate.month == today.month &&
                 aptDate.day == today.day;
        }).length.toString(),
        'icon': Icons.calendar_today,
        'color': const Color(0xFF10B981),
        'trend': '+5%',
        'subtitle': 'Scheduled today',
      },
      {
        'title': 'Avg Progress',
        'value': '${(_patients.fold<double>(0, (sum, patient) => sum + (patient['progress'] ?? 0)) / (_patients.isNotEmpty ? _patients.length : 1)).round()}%',
        'icon': Icons.trending_up,
        'color': const Color(0xFF3B82F6),
        'trend': '+8%',
        'subtitle': 'Patient improvement',
      },
      {
        'title': 'Rating',
        'value': '${_therapistData?['rating'] ?? 4.9}',
        'icon': Icons.star,
        'color': const Color(0xFFF59E0B),
        'trend': '+0.1',
        'subtitle': 'Patient satisfaction',
      },
    ];
  }

  void _loadMockData() {
    _therapistData = {
      'id': widget.therapistId,
      'name': 'Dr. Sarah Johnson',
      'email': '<EMAIL>',
      'specialization': 'Anxiety & Depression',
      'experience': '8 years',
      'rating': 4.9,
      'location': 'New York, NY',
      'avatar': 'https://via.placeholder.com/150',
    };
  }

  Future<void> _logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.clear();
    if (mounted) {
      Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(builder: (context) => const LoginPage()),
        (route) => false,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: _isLoading ? _buildLoadingScreen() : _buildMainContent(),
      bottomNavigationBar: _buildBottomNavigation(),
    );
  }

  Widget _buildLoadingScreen() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF8B5CF6),
            Color(0xFFA855F7),
            Color(0xFFC084FC),
          ],
        ),
      ),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
            SizedBox(height: 24),
            Text(
              'Loading Dashboard...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMainContent() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            Expanded(
              child: _buildBody(),
            ),
          ],
        ),
      ),
    );
  }
