const express = require('express');
const router = express.Router();
const mongoose = require('mongoose');

// Test database connection endpoint
router.get('/connection', async (req, res) => {
  try {
    const dbState = mongoose.connection.readyState;
    const states = {
      0: 'disconnected',
      1: 'connected',
      2: 'connecting',
      3: 'disconnecting'
    };

    const connectionInfo = {
      status: states[dbState],
      database: mongoose.connection.name,
      host: mongoose.connection.host,
      port: mongoose.connection.port,
      collections: []
    };

    // Get list of collections
    if (dbState === 1) {
      const collections = await mongoose.connection.db.listCollections().toArray();
      connectionInfo.collections = collections.map(col => col.name);
    }

    res.json({
      success: true,
      message: 'Database connection test successful',
      connection: connectionInfo
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Database connection test failed',
      error: error.message
    });
  }
});

// Create sample data endpoint
router.post('/seed', async (req, res) => {
  try {
    // Import models
    const { User, <PERSON><PERSON>, JournalEntry, Therapist, Appointment, Payment } = require('../models');

    // Create sample users
    const sampleUsers = [
      {
        username: 'john_doe',
        email: '<EMAIL>',
        passwordHash: 'hashed_password_123',
        role: 'user',
        location: {
          type: 'Point',
          coordinates: [-74.0060, 40.7128] // NYC coordinates [longitude, latitude]
        }
      },
      {
        username: 'dr_smith',
        email: '<EMAIL>',
        passwordHash: 'hashed_password_456',
        role: 'therapist',
        location: {
          type: 'Point',
          coordinates: [-73.9851, 40.7589]
        }
      },
      {
        username: 'admin_user',
        email: '<EMAIL>',
        passwordHash: 'hashed_password_789',
        role: 'admin',
        location: {
          type: 'Point',
          coordinates: [-74.0060, 40.7128]
        }
      }
    ];

    // Clear existing data (optional)
    await User.deleteMany({});
    await Mood.deleteMany({});
    await JournalEntry.deleteMany({});
    await Therapist.deleteMany({});

    // Insert sample users
    const createdUsers = await User.insertMany(sampleUsers);
    console.log(`Created ${createdUsers.length} sample users`);

    // Create sample mood entries
    const sampleMoods = [
      {
        userId: createdUsers[0]._id.toString(),
        mood: 'happy',
        note: 'Had a great day at work today!',
        date: new Date()
      },
      {
        userId: createdUsers[0]._id.toString(),
        mood: 'anxious',
        note: 'Feeling a bit worried about tomorrow\'s presentation.',
        date: new Date(Date.now() - 24 * 60 * 60 * 1000) // Yesterday
      },
      {
        userId: createdUsers[0]._id.toString(),
        mood: 'calm',
        note: 'Meditation session was very relaxing.',
        date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000) // 2 days ago
      }
    ];

    const createdMoods = await Mood.insertMany(sampleMoods);
    console.log(`Created ${createdMoods.length} sample mood entries`);

    // Create sample journal entries
    const sampleJournals = [
      {
        userId: createdUsers[0]._id,
        entryDate: new Date(),
        content: 'Today was a productive day. I managed to complete all my tasks and even had time for a walk in the park.',
        sentimentScore: 0.8
      },
      {
        userId: createdUsers[0]._id,
        entryDate: new Date(Date.now() - 24 * 60 * 60 * 1000),
        content: 'Feeling a bit overwhelmed with work lately. Need to find better work-life balance.',
        sentimentScore: -0.3
      }
    ];

    const createdJournals = await JournalEntry.insertMany(sampleJournals);
    console.log(`Created ${createdJournals.length} sample journal entries`);

    res.json({
      success: true,
      message: 'Sample data created successfully',
      data: {
        users: createdUsers.length,
        moods: createdMoods.length,
        journals: createdJournals.length,
        userIds: createdUsers.map(u => u._id),
        moodIds: createdMoods.map(m => m._id),
        journalIds: createdJournals.map(j => j._id)
      }
    });

  } catch (error) {
    console.error('Error seeding database:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create sample data',
      error: error.message
    });
  }
});

// Get all users endpoint
router.get('/users', async (req, res) => {
  try {
    const { User } = require('../models');
    const users = await User.find({}).select('-passwordHash'); // Exclude password hash

    res.json({
      success: true,
      count: users.length,
      users: users
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to fetch users',
      error: error.message
    });
  }
});

// Get all mood entries endpoint
router.get('/moods', async (req, res) => {
  try {
    const { Mood } = require('../models');
    const moods = await Mood.find({});

    res.json({
      success: true,
      count: moods.length,
      moods: moods
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to fetch moods',
      error: error.message
    });
  }
});

// Get all journal entries endpoint
router.get('/journals', async (req, res) => {
  try {
    const { JournalEntry } = require('../models');
    const journals = await JournalEntry.find({}).populate('userId', 'username');

    res.json({
      success: true,
      count: journals.length,
      journals: journals
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to fetch journals',
      error: error.message
    });
  }
});

// Clear all data endpoint (use with caution)
router.delete('/clear', async (req, res) => {
  try {
    const { User, Mood, JournalEntry, Payment, Appointment, Therapist } = require('../models');

    const results = await Promise.all([
      User.deleteMany({}),
      Mood.deleteMany({}),
      JournalEntry.deleteMany({}),
      Payment.deleteMany({}),
      Appointment.deleteMany({}),
      Therapist.deleteMany({})
    ]);

    res.json({
      success: true,
      message: 'All data cleared successfully',
      deletedCounts: {
        users: results[0].deletedCount,
        moods: results[1].deletedCount,
        journals: results[2].deletedCount,
        payments: results[3].deletedCount,
        appointments: results[4].deletedCount,
        therapists: results[5].deletedCount
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to clear data',
      error: error.message
    });
  }
});

module.exports = router;
