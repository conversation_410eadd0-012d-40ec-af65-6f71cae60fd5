const express = require('express');
const router = express.Router();
const { validatePayment } = require('../middleware/validators');
const PaymentController = require('../controllers/paymentController');

// Original routes
router.get('/', PaymentController.getAllPayments);
router.get('/user/:id', PaymentController.getPaymentById);
router.post('/', validatePayment, PaymentController.createPayment);
router.put('/:id', validatePayment, PaymentController.updatePayment);
router.get('/user/:userId', PaymentController.getUserPayments);

// New Stripe integration routes
router.post('/create-intent', PaymentController.createPaymentIntent);
router.post('/confirm', PaymentController.confirmPayment);
router.get('/history/:userId', PaymentController.getPaymentHistory);
router.get('/pricing', PaymentController.getAppointmentPricing);
router.post('/refund', PaymentController.processRefund);
router.post('/save-method', PaymentController.savePaymentMethod);
router.get('/methods/:userId', PaymentController.getSavedPaymentMethods);
router.delete('/methods/:paymentMethodId', PaymentController.deletePaymentMethod);
router.get('/status/:paymentIntentId', PaymentController.getPaymentStatus);

module.exports = router;