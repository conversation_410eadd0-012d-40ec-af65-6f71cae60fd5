import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;

class JournalingScreen extends StatefulWidget {
  final String userId;

  const JournalingScreen({super.key, required this.userId});

  @override
  _JournalingScreenState createState() => _JournalingScreenState();
}

class _JournalingScreenState extends State<JournalingScreen> {
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _contentController = TextEditingController();
  List<Map<String, dynamic>> journalEntries = [];
  bool isLoading = false;
  String selectedMood = 'neutral';

  final List<Map<String, String>> moodOptions = [
    {'value': 'very_happy', 'emoji': '😄', 'label': 'Very Happy'},
    {'value': 'happy', 'emoji': '😊', 'label': 'Happy'},
    {'value': 'neutral', 'emoji': '😐', 'label': 'Neutral'},
    {'value': 'sad', 'emoji': '😔', 'label': 'Sad'},
    {'value': 'very_sad', 'emoji': '😢', 'label': 'Very Sad'},
  ];

  final List<String> journalPrompts = [
    "What am I grateful for today?",
    "What challenged me today and how did I handle it?",
    "What made me smile today?",
    "How am I feeling right now and why?",
    "What did I learn about myself today?",
    "What would I like to improve tomorrow?",
    "Describe a moment when I felt proud of myself",
    "What are three things that went well today?",
    "How did I take care of myself today?",
    "What emotions did I experience today?",
  ];

  @override
  void initState() {
    super.initState();
    _fetchJournalEntries();
  }

  Future<void> _fetchJournalEntries() async {
    try {
      final response = await http.get(
        Uri.parse('http://localhost:3000/api/journal-entries/${widget.userId}'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        setState(() {
          journalEntries = List<Map<String, dynamic>>.from(data['entries'] ?? []);
        });
      }
    } catch (e) {
      print('Error fetching journal entries: $e');
    }
  }

  Future<void> _saveJournalEntry() async {
    if (_titleController.text.trim().isEmpty || _contentController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please fill in both title and content')),
      );
      return;
    }

    setState(() {
      isLoading = true;
    });

    try {
      final response = await http.post(
        Uri.parse('http://localhost:3000/api/journal-entries'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'userId': widget.userId,
          'title': _titleController.text.trim(),
          'content': _contentController.text.trim(),
          'mood': selectedMood,
          'date': DateTime.now().toIso8601String(),
        }),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Journal entry saved successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        _clearForm();
        _fetchJournalEntries();
      } else {
        throw Exception('Failed to save journal entry');
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $e')),
      );
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  void _clearForm() {
    _titleController.clear();
    _contentController.clear();
    setState(() {
      selectedMood = 'neutral';
    });
  }

  void _usePrompt(String prompt) {
    _titleController.text = "Daily Reflection";
    _contentController.text = "$prompt\n\n";
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Journaling'),
        backgroundColor: Colors.green,
      ),
      body: DefaultTabController(
        length: 3,
        child: Column(
          children: [
            const TabBar(
              labelColor: Colors.green,
              tabs: [
                Tab(text: 'Write', icon: Icon(Icons.edit)),
                Tab(text: 'Prompts', icon: Icon(Icons.lightbulb)),
                Tab(text: 'Entries', icon: Icon(Icons.book)),
              ],
            ),
            Expanded(
              child: TabBarView(
                children: [
                  _buildWriteTab(),
                  _buildPromptsTab(),
                  _buildEntriesTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWriteTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'New Journal Entry',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),

          // Mood selection
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'How are you feeling?',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: moodOptions.map((mood) {
                      final isSelected = selectedMood == mood['value'];
                      return GestureDetector(
                        onTap: () {
                          setState(() {
                            selectedMood = mood['value']!;
                          });
                        },
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: isSelected ? Colors.green.withOpacity(0.2) : null,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: isSelected ? Colors.green : Colors.grey.shade300,
                            ),
                          ),
                          child: Column(
                            children: [
                              Text(mood['emoji']!, style: const TextStyle(fontSize: 24)),
                              const SizedBox(height: 4),
                              Text(
                                mood['label']!,
                                style: TextStyle(
                                  fontSize: 10,
                                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),

          // Title input
          TextField(
            controller: _titleController,
            decoration: const InputDecoration(
              labelText: 'Title',
              hintText: 'Give your entry a title...',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 16),

          // Content input
          TextField(
            controller: _contentController,
            maxLines: 12,
            decoration: const InputDecoration(
              labelText: 'Your thoughts...',
              hintText: 'What\'s on your mind today?',
              border: OutlineInputBorder(),
              alignLabelWithHint: true,
            ),
          ),
          const SizedBox(height: 20),

          // Save button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: isLoading ? null : _saveJournalEntry,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: isLoading
                  ? const CircularProgressIndicator(color: Colors.white)
                  : const Text(
                      'Save Entry',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPromptsTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Writing Prompts',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Need inspiration? Try one of these prompts:',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 20),
          Expanded(
            child: ListView.builder(
              itemCount: journalPrompts.length,
              itemBuilder: (context, index) {
                return Card(
                  margin: const EdgeInsets.only(bottom: 12),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: Colors.green.withOpacity(0.2),
                      child: Text(
                        '${index + 1}',
                        style: const TextStyle(
                          color: Colors.green,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    title: Text(journalPrompts[index]),
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: () {
                      _usePrompt(journalPrompts[index]);
                      DefaultTabController.of(context).animateTo(0);
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEntriesTab() {
    if (journalEntries.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.book, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No journal entries yet',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            SizedBox(height: 8),
            Text(
              'Start writing to see your entries here',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: journalEntries.length,
      itemBuilder: (context, index) {
        final entry = journalEntries[index];
        final date = DateTime.parse(entry['date']);
        final mood = moodOptions.firstWhere(
          (m) => m['value'] == entry['mood'],
          orElse: () => moodOptions[2], // default to neutral
        );

        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ExpansionTile(
            leading: Text(
              mood['emoji']!,
              style: const TextStyle(fontSize: 24),
            ),
            title: Text(
              entry['title'],
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Text(
              '${date.day}/${date.month}/${date.year} at ${date.hour}:${date.minute.toString().padLeft(2, '0')}',
            ),
            children: [
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      entry['content'],
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Chip(
                          avatar: Text(mood['emoji']!),
                          label: Text(mood['label']!),
                          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    super.dispose();
  }
}
