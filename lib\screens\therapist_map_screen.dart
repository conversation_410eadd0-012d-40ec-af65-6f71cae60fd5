import 'package:flutter/material.dart';
import 'package:myapp/api_service.dart';

class TherapistMapScreen extends StatefulWidget {
  final String userId;

  const TherapistMapScreen({
    super.key,
    required this.userId,
  });

  @override
  State<TherapistMapScreen> createState() => _TherapistMapScreenState();
}

class _TherapistMapScreenState extends State<TherapistMapScreen> {
  List<Map<String, dynamic>> _nearbyTherapists = [];
  bool _isLoading = false;
  String _statusMessage = 'Finding therapists near you...';
  double _selectedRadius = 10.0;
  String _userLocation = 'Location not available';

  @override
  void initState() {
    super.initState();
    _loadTherapists();
  }

  Future<void> _loadTherapists() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'Getting your location...';
    });

    try {
      // Get current location
      final position = await _geoService.getCurrentLocation();

      if (position != null) {
        setState(() {
          _currentPosition = position;
          _statusMessage = 'Finding nearby therapists...';
        });

        // Find nearby therapists
        final therapists = await _geoService.findNearbyTherapists(
          latitude: position.latitude,
          longitude: position.longitude,
          radius: _selectedRadius,
        );

        setState(() {
          _nearbyTherapists = therapists;
          _statusMessage = 'Found ${therapists.length} therapists nearby';
        });

        // Create map markers
        _createMapMarkers();
      } else {
        setState(() {
          _statusMessage = 'Unable to get location. Showing all therapists.';
        });

        // Fallback: get all therapists
        final result = await ApiService.getAllTherapists();
        if (result['success']) {
          setState(() {
            _nearbyTherapists = List<Map<String, dynamic>>.from(
                result['data']['therapists'] ?? []);
          });
        }
      }
    } catch (e) {
      setState(() {
        _statusMessage = 'Error: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Therapist Map'),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadTherapists,
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF00BCD4),
              Color(0xFF26C6DA),
              Color(0xFF4DD0E1),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Status and Controls
              Container(
                margin: const EdgeInsets.all(16),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Icon(
                          _isLoading
                              ? Icons.location_searching
                              : Icons.location_on,
                          color: _isLoading ? Colors.orange : Colors.teal,
                          size: 24,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            _statusMessage,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                    if (_currentPosition != null) ...[
                      const SizedBox(height: 12),
                      Text(
                        'Your location: ${_currentPosition!.latitude.toStringAsFixed(4)}, ${_currentPosition!.longitude.toStringAsFixed(4)}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        const Text('Search radius: '),
                        Expanded(
                          child: Slider(
                            value: _selectedRadius,
                            min: 1.0,
                            max: 50.0,
                            divisions: 49,
                            label: '${_selectedRadius.round()} km',
                            activeColor: Colors.teal,
                            onChanged: (value) {
                              setState(() {
                                _selectedRadius = value;
                              });
                            },
                            onChangeEnd: (value) {
                              _loadTherapists();
                            },
                          ),
                        ),
                        Text('${_selectedRadius.round()} km'),
                      ],
                    ),
                  ],
                ),
              ),

              // Map Placeholder and Therapist List
              Expanded(
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      // Google Map
                      Container(
                        height: 300,
                        width: double.infinity,
                        margin: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey[300]!),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: GoogleMap(
                            initialCameraPosition: _currentPosition != null
                                ? CameraPosition(
                                    target: LatLng(
                                      _currentPosition!.latitude,
                                      _currentPosition!.longitude,
                                    ),
                                    zoom: 12,
                                  )
                                : _initialCameraPosition,
                            markers: _markers,
                            onMapCreated: (GoogleMapController controller) {
                              _mapController.complete(controller);
                            },
                            myLocationEnabled: true,
                            myLocationButtonEnabled: true,
                            zoomControlsEnabled: true,
                            mapType: MapType.normal,
                          ),
                        ),
                      ),

                      // Therapist List
                      Expanded(
                        child: _isLoading
                            ? const Center(
                                child: CircularProgressIndicator(
                                    color: Colors.teal),
                              )
                            : _nearbyTherapists.isEmpty
                                ? const Center(
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Icon(
                                          Icons.search_off,
                                          size: 48,
                                          color: Colors.grey,
                                        ),
                                        SizedBox(height: 16),
                                        Text(
                                          'No therapists found nearby',
                                          style: TextStyle(
                                            fontSize: 16,
                                            color: Colors.grey,
                                          ),
                                        ),
                                        Text(
                                          'Try increasing the search radius',
                                          style: TextStyle(
                                            fontSize: 14,
                                            color: Colors.grey,
                                          ),
                                        ),
                                      ],
                                    ),
                                  )
                                : ListView.builder(
                                    padding: const EdgeInsets.all(16),
                                    itemCount: _nearbyTherapists.length,
                                    itemBuilder: (context, index) {
                                      final therapist =
                                          _nearbyTherapists[index];
                                      return _buildTherapistCard(therapist);
                                    },
                                  ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  void _createMapMarkers() {
    _markers.clear();

    // Add user location marker
    if (_currentPosition != null) {
      _markers.add(
        Marker(
          markerId: const MarkerId('user_location'),
          position: LatLng(
            _currentPosition!.latitude,
            _currentPosition!.longitude,
          ),
          infoWindow: const InfoWindow(
            title: 'Your Location',
            snippet: 'You are here',
          ),
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
        ),
      );
    }

    // Add therapist markers
    for (int i = 0; i < _nearbyTherapists.length; i++) {
      final therapist = _nearbyTherapists[i];
      final location = therapist['location'];

      if (location != null &&
          location['latitude'] != null &&
          location['longitude'] != null) {
        _markers.add(
          Marker(
            markerId: MarkerId('therapist_$i'),
            position: LatLng(
              location['latitude'].toDouble(),
              location['longitude'].toDouble(),
            ),
            infoWindow: InfoWindow(
              title: therapist['name'] ?? 'Therapist',
              snippet: therapist['specialty'] ?? 'General Therapy',
            ),
            icon:
                BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
            onTap: () => _showTherapistDetails(therapist),
          ),
        );
      }
    }

    setState(() {});
  }

  Widget _buildMapMarker(String name, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.3),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            name == 'You' ? Icons.person_pin : Icons.local_hospital,
            color: Colors.white,
            size: 16,
          ),
          const SizedBox(width: 4),
          Text(
            name,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTherapistCard(Map<String, dynamic> therapist) {
    final name = therapist['name'] ?? 'Unknown Therapist';
    final specialty = therapist['specialty'] ?? 'General Therapy';
    final distance = therapist['distanceText'] ?? 'Distance unknown';
    final rating = therapist['rating']?.toString() ?? '4.5';
    final location = therapist['location'];
    final address = location?['address'] ?? 'Address not available';

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: Colors.teal.withOpacity(0.1),
                  child: const Icon(
                    Icons.person,
                    color: Colors.teal,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        specialty,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.star,
                          color: Colors.amber,
                          size: 16,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          rating,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.teal.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        distance,
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.teal,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                const Icon(
                  Icons.location_on,
                  size: 16,
                  color: Colors.grey,
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    address,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _showTherapistDetails(therapist),
                    icon: const Icon(Icons.info_outline, size: 16),
                    label: const Text('Details'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.teal,
                      side: const BorderSide(color: Colors.teal),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _bookAppointment(therapist),
                    icon: const Icon(Icons.calendar_today, size: 16),
                    label: const Text('Book'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.teal,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showTherapistDetails(Map<String, dynamic> therapist) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(therapist['name'] ?? 'Therapist Details'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Specialty: ${therapist['specialty'] ?? 'General'}'),
            const SizedBox(height: 8),
            Text('Rating: ${therapist['rating'] ?? 'N/A'} ⭐'),
            const SizedBox(height: 8),
            if (therapist['distanceText'] != null)
              Text('Distance: ${therapist['distanceText']}'),
            const SizedBox(height: 8),
            if (therapist['location']?['address'] != null)
              Text('Address: ${therapist['location']['address']}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _bookAppointment(therapist);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.teal),
            child: const Text('Book Appointment'),
          ),
        ],
      ),
    );
  }

  void _bookAppointment(Map<String, dynamic> therapist) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Booking appointment with ${therapist['name']}...'),
        backgroundColor: Colors.teal,
        action: SnackBarAction(
          label: 'View',
          textColor: Colors.white,
          onPressed: () {
            // Navigate to booking screen
          },
        ),
      ),
    );
  }
}
