import 'package:flutter/material.dart';
import 'package:myapp/services/video_call_service.dart';
import 'package:myapp/services/geolocation_service.dart';
import 'package:myapp/services/semantic_analysis_service.dart';
import 'package:myapp/services/payment_service.dart';
import 'package:myapp/screens/video_call_screen.dart';
import 'package:myapp/screens/location_screen.dart';
import 'package:myapp/screens/semantic_analysis_screen.dart';
import 'package:myapp/screens/payment_screen.dart';

class FeaturesDemoScreen extends StatefulWidget {
  final String userId;
  
  const FeaturesDemoScreen({
    super.key,
    required this.userId,
  });

  @override
  State<FeaturesDemoScreen> createState() => _FeaturesDemoScreenState();
}

class _FeaturesDemoScreenState extends State<FeaturesDemoScreen> {
  bool _isLoading = false;
  String _statusMessage = '';

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  Future<void> _initializeServices() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'Initializing services...';
    });

    try {
      // Initialize all services
      await VideoCallService().initialize();
      await PaymentService.initializeStripe();
      
      setState(() {
        _statusMessage = 'All services initialized successfully!';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Error initializing services: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('MindEase Features Demo'),
        backgroundColor: Colors.indigo,
        foregroundColor: Colors.white,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF8B5CF6),
              Color(0xFFA855F7),
              Color(0xFFC084FC),
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Status Card
                Card(
                  elevation: 8,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      children: [
                        Icon(
                          _isLoading ? Icons.hourglass_empty : Icons.check_circle,
                          size: 48,
                          color: _isLoading ? Colors.orange : Colors.green,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          _statusMessage,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        if (_isLoading) ...[
                          const SizedBox(height: 16),
                          const LinearProgressIndicator(),
                        ],
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: 24),
                
                // Features Grid
                Expanded(
                  child: GridView.count(
                    crossAxisCount: 2,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                    children: [
                      _buildFeatureCard(
                        title: 'Video Call',
                        icon: Icons.video_call,
                        color: Colors.blue,
                        description: 'High-quality video therapy sessions',
                        onTap: () => _testVideoCall(),
                      ),
                      _buildFeatureCard(
                        title: 'Geo Location',
                        icon: Icons.location_on,
                        color: Colors.green,
                        description: 'Find nearby therapists',
                        onTap: () => _testGeoLocation(),
                      ),
                      _buildFeatureCard(
                        title: 'AI Analysis',
                        icon: Icons.psychology,
                        color: Colors.purple,
                        description: 'Semantic mood analysis',
                        onTap: () => _testSemanticAnalysis(),
                      ),
                      _buildFeatureCard(
                        title: 'Payments',
                        icon: Icons.payment,
                        color: Colors.orange,
                        description: 'Secure payment processing',
                        onTap: () => _testPayments(),
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Test All Features Button
                ElevatedButton(
                  onPressed: _isLoading ? null : _testAllFeatures,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: Colors.indigo,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 4,
                  ),
                  child: const Text(
                    'Test All Features',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureCard({
    required String title,
    required IconData icon,
    required Color color,
    required String description,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  size: 32,
                  color: color,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                description,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _testVideoCall() async {
    try {
      setState(() {
        _statusMessage = 'Testing video call...';
      });

      // Navigate to video call screen
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => VideoCallScreen(
            channelName: 'demo_channel_${DateTime.now().millisecondsSinceEpoch}',
            therapistName: 'Dr. Demo',
            appointmentId: 'demo_appointment',
          ),
        ),
      );
    } catch (e) {
      _showErrorDialog('Video Call Error', 'Failed to start video call: $e');
    }
  }

  Future<void> _testGeoLocation() async {
    try {
      setState(() {
        _statusMessage = 'Testing geolocation...';
      });

      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => LocationScreen(userId: widget.userId),
        ),
      );
    } catch (e) {
      _showErrorDialog('Geolocation Error', 'Failed to access location: $e');
    }
  }

  Future<void> _testSemanticAnalysis() async {
    try {
      setState(() {
        _statusMessage = 'Testing semantic analysis...';
      });

      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => SemanticAnalysisScreen(userId: widget.userId),
        ),
      );
    } catch (e) {
      _showErrorDialog('Semantic Analysis Error', 'Failed to start analysis: $e');
    }
  }

  Future<void> _testPayments() async {
    try {
      setState(() {
        _statusMessage = 'Testing payments...';
      });

      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => PaymentScreen(
            userId: widget.userId,
            amount: 80.0,
            appointmentId: 'demo_appointment',
            therapistName: 'Dr. Demo',
          ),
        ),
      );
    } catch (e) {
      _showErrorDialog('Payment Error', 'Failed to start payment: $e');
    }
  }

  Future<void> _testAllFeatures() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'Testing all features...';
    });

    try {
      // Test Video Call Service
      setState(() {
        _statusMessage = 'Testing video call service...';
      });
      await Future.delayed(const Duration(seconds: 1));
      
      // Test Geolocation Service
      setState(() {
        _statusMessage = 'Testing geolocation service...';
      });
      final geoService = GeolocationService();
      await geoService.getCurrentLocation();
      
      // Test Semantic Analysis Service
      setState(() {
        _statusMessage = 'Testing semantic analysis service...';
      });
      final semanticService = SemanticAnalysisService();
      semanticService.analyzeTextSentiment('I feel happy today!');
      
      // Test Payment Service
      setState(() {
        _statusMessage = 'Testing payment service...';
      });
      await Future.delayed(const Duration(seconds: 1));
      
      setState(() {
        _statusMessage = 'All features tested successfully! ✅';
      });
      
      _showSuccessDialog('Success!', 'All four features are working correctly:\n\n✅ Video Call\n✅ Geolocation\n✅ Semantic Analysis\n✅ Payments');
      
    } catch (e) {
      setState(() {
        _statusMessage = 'Feature test failed: $e';
      });
      _showErrorDialog('Test Failed', 'Some features failed to initialize: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showErrorDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showSuccessDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Great!'),
          ),
        ],
      ),
    );
  }
}
