// routes/semanticRoutes.js
const express = require('express');
const router = express.Router();
const semanticController = require('../controllers/semanticController');

// Mood analysis
router.post('/mood-analysis', semanticController.analyzeMood);

// Sentiment analysis
router.post('/sentiment-analysis', semanticController.analyzeSentiment);

// Emotion extraction
router.post('/emotion-extraction', semanticController.extractEmotions);

// Generate personalized insights
router.post('/generate-insights', semanticController.generateInsights);

// Analyze journal entry
router.post('/analyze-journal', semanticController.analyzeJournal);

// Get mood trends
router.get('/mood-trends/:userId', semanticController.getMoodTrends);

// Generate coping strategies
router.post('/coping-strategies', semanticController.generateCopingStrategies);

// Detect crisis indicators
router.post('/crisis-detection', semanticController.detectCrisisIndicators);

module.exports = router;
