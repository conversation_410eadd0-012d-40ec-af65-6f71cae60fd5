const { User } = require('../models');
const bcrypt = require('bcryptjs');

class GoogleAuthController {
  static async googleLogin(req, res) {
    try {
      const { googleId, email, username, photoUrl, idToken } = req.body;

      // Validate required fields
      if (!googleId || !email) {
        return res.status(400).json({ message: "Google ID and email are required" });
      }

      // Check if user already exists with this Google ID
      let user = await User.findOne({ 
        $or: [
          { googleId: googleId },
          { email: email }
        ]
      });

      if (user) {
        // User exists, update Google ID if not set
        if (!user.googleId) {
          user.googleId = googleId;
          user.photoUrl = photoUrl;
          await user.save();
        }

        console.log(`✅ Google login successful for existing user: ${email}`);
        return res.status(200).json({
          message: "Login successful",
          user: {
            _id: user._id,
            username: user.username,
            email: user.email,
            role: user.role,
            photoUrl: user.photoUrl,
            location: user.location,
            moodEntries: user.moodEntries,
            recommendations: user.recommendations,
            paymentIds: user.paymentIds
          },
          isNewUser: false
        });
      } else {
        // Create new user
        const newUser = new User({
          googleId: googleId,
          username: username || 'Google User',
          email: email,
          photoUrl: photoUrl,
          role: 'user', // Default role
          passwordHash: await bcrypt.hash(Math.random().toString(36), 10), // Random password for Google users
          location: {
            type: 'Point',
            coordinates: [0, 0] // Default coordinates
          },
          moodEntries: [],
          recommendations: [],
          paymentIds: []
        });

        await newUser.save();

        console.log(`✅ New Google user created: ${email}`);
        return res.status(201).json({
          message: "Account created and login successful",
          user: {
            _id: newUser._id,
            username: newUser.username,
            email: newUser.email,
            role: newUser.role,
            photoUrl: newUser.photoUrl,
            location: newUser.location,
            moodEntries: newUser.moodEntries,
            recommendations: newUser.recommendations,
            paymentIds: newUser.paymentIds
          },
          isNewUser: true
        });
      }

    } catch (error) {
      console.error("Google Login Error:", error.message);
      console.error(error.stack);
      res.status(500).json({ message: "Error in Google login process" });
    }
  }
}

module.exports = GoogleAuthController;
