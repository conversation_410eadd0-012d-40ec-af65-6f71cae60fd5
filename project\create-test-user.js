const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/mindease', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

// Define User schema (simplified)
const userSchema = new mongoose.Schema({
  username: String,
  email: String,
  passwordHash: String,
  role: String,
  location: {
    type: {
      type: String,
      enum: ['Point'],
      default: 'Point'
    },
    coordinates: {
      type: [Number],
      default: [0, 0]
    }
  },
  moodEntries: [String],
  recommendations: [String],
  paymentIds: [String]
});

const User = mongoose.model('User', userSchema);

async function createTestUser() {
  try {
    // Hash the password
    const passwordHash = await bcrypt.hash('password123', 10);

    // Delete existing test user if exists
    await User.deleteOne({ email: '<EMAIL>' });

    // Create new test user
    const testUser = new User({
      username: 'test_user',
      email: '<EMAIL>',
      passwordHash: passwordHash,
      role: 'user',
      location: {
        type: 'Point',
        coordinates: [-74.0060, 40.7128]
      },
      moodEntries: [],
      recommendations: [],
      paymentIds: []
    });

    await testUser.save();
    console.log('✅ Test user created successfully!');
    console.log('Email: <EMAIL>');
    console.log('Password: password123');
    console.log('Role: user');

    // Also create a demo user for easy testing
    const demoPasswordHash = await bcrypt.hash('demo123', 10);
    await User.deleteOne({ email: '<EMAIL>' });

    const demoUser = new User({
      username: 'demo_user',
      email: '<EMAIL>',
      passwordHash: demoPasswordHash,
      role: 'user',
      location: {
        type: 'Point',
        coordinates: [-74.0060, 40.7128]
      },
      moodEntries: [],
      recommendations: [],
      paymentIds: []
    });

    await demoUser.save();
    console.log('✅ Demo user created successfully!');
    console.log('Email: <EMAIL>');
    console.log('Password: demo123');
    console.log('Role: user');

    mongoose.connection.close();
  } catch (error) {
    console.error('❌ Error creating test user:', error);
    mongoose.connection.close();
  }
}

createTestUser();
