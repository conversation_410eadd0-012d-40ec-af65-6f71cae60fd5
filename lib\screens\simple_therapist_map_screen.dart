import 'package:flutter/material.dart';
import 'package:myapp/api_service.dart';

class TherapistMapScreen extends StatefulWidget {
  final String userId;

  const TherapistMapScreen({
    super.key,
    required this.userId,
  });

  @override
  State<TherapistMapScreen> createState() => _TherapistMapScreenState();
}

class _TherapistMapScreenState extends State<TherapistMapScreen> {
  List<Map<String, dynamic>> _nearbyTherapists = [];
  bool _isLoading = false;
  String _statusMessage = 'Finding therapists near you...';
  double _selectedRadius = 10.0;

  @override
  void initState() {
    super.initState();
    _loadTherapists();
  }

  Future<void> _loadTherapists() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'Finding therapists...';
    });

    try {
      // Get all therapists from API
      final result = await ApiService.getAllTherapists();
      if (result['success']) {
        setState(() {
          _nearbyTherapists = List<Map<String, dynamic>>.from(
              result['data']['therapists'] ?? []);
          _statusMessage = 'Found ${_nearbyTherapists.length} therapists';
        });
      } else {
        setState(() {
          _statusMessage = 'Error loading therapists';
        });
      }
    } catch (e) {
      setState(() {
        _statusMessage = 'Error: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Therapist Map'),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadTherapists,
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF00BCD4),
              Color(0xFF26C6DA),
              Color(0xFF4DD0E1),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Status and Controls
              Container(
                margin: const EdgeInsets.all(16),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Icon(
                          _isLoading
                              ? Icons.location_searching
                              : Icons.location_on,
                          color: _isLoading ? Colors.orange : Colors.teal,
                          size: 24,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            _statusMessage,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        const Text('Search radius: '),
                        Expanded(
                          child: Slider(
                            value: _selectedRadius,
                            min: 1.0,
                            max: 50.0,
                            divisions: 49,
                            label: '${_selectedRadius.round()} km',
                            activeColor: Colors.teal,
                            onChanged: (value) {
                              setState(() {
                                _selectedRadius = value;
                              });
                            },
                            onChangeEnd: (value) {
                              _loadTherapists();
                            },
                          ),
                        ),
                        Text('${_selectedRadius.round()} km'),
                      ],
                    ),
                  ],
                ),
              ),

              // Map Placeholder and Therapist List
              Expanded(
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      // Interactive Map Placeholder
                      Container(
                        height: 250,
                        width: double.infinity,
                        margin: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey[300]!),
                        ),
                        child: Stack(
                          children: [
                            // Map background with grid pattern
                            Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(8),
                                color: Colors.grey[50],
                              ),
                              child: CustomPaint(
                                size: Size.infinite,
                                painter: MapGridPainter(),
                              ),
                            ),
                            // Map overlay with info
                            Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(16),
                                    decoration: BoxDecoration(
                                      color: Colors.teal.withOpacity(0.9),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Column(
                                      children: [
                                        const Icon(
                                          Icons.map,
                                          size: 32,
                                          color: Colors.white,
                                        ),
                                        const SizedBox(height: 8),
                                        const Text(
                                          'Map View',
                                          style: TextStyle(
                                            fontSize: 14,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.white,
                                          ),
                                        ),
                                        Text(
                                          '${_nearbyTherapists.length} therapists',
                                          style: const TextStyle(
                                            fontSize: 11,
                                            color: Colors.white,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            // Simulated map markers
                            if (_nearbyTherapists.isNotEmpty) ...[
                              Positioned(
                                top: 30,
                                left: 50,
                                child: _buildMapMarker('Dr. Smith', Colors.red),
                              ),
                              Positioned(
                                top: 80,
                                right: 60,
                                child:
                                    _buildMapMarker('Dr. Johnson', Colors.blue),
                              ),
                              Positioned(
                                bottom: 40,
                                left: 80,
                                child: _buildMapMarker(
                                    'Dr. Williams', Colors.green),
                              ),
                              Positioned(
                                bottom: 60,
                                right: 40,
                                child: _buildMapMarker('You', Colors.orange),
                              ),
                            ],
                          ],
                        ),
                      ),

                      // Therapist List
                      Expanded(
                        child: _isLoading
                            ? const Center(
                                child: CircularProgressIndicator(
                                    color: Colors.teal),
                              )
                            : _nearbyTherapists.isEmpty
                                ? const Center(
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Icon(
                                          Icons.search_off,
                                          size: 48,
                                          color: Colors.grey,
                                        ),
                                        SizedBox(height: 16),
                                        Text(
                                          'No therapists found nearby',
                                          style: TextStyle(
                                            fontSize: 16,
                                            color: Colors.grey,
                                          ),
                                        ),
                                        Text(
                                          'Try increasing the search radius',
                                          style: TextStyle(
                                            fontSize: 14,
                                            color: Colors.grey,
                                          ),
                                        ),
                                      ],
                                    ),
                                  )
                                : ListView.builder(
                                    padding: const EdgeInsets.all(16),
                                    itemCount: _nearbyTherapists.length,
                                    itemBuilder: (context, index) {
                                      final therapist =
                                          _nearbyTherapists[index];
                                      return _buildTherapistCard(therapist);
                                    },
                                  ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMapMarker(String name, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.3),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            name == 'You' ? Icons.person_pin : Icons.local_hospital,
            color: Colors.white,
            size: 16,
          ),
          const SizedBox(width: 4),
          Text(
            name,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTherapistCard(Map<String, dynamic> therapist) {
    final name = therapist['name'] ?? 'Unknown Therapist';
    final specialty = therapist['specialty'] ?? 'General Therapy';
    final rating = therapist['rating']?.toString() ?? '4.5';

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: Colors.teal.withOpacity(0.1),
                  child: const Icon(
                    Icons.person,
                    color: Colors.teal,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        specialty,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.star,
                          color: Colors.amber,
                          size: 16,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          rating,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.teal.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Text(
                        'Available',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.teal,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _showTherapistDetails(therapist),
                    icon: const Icon(Icons.info_outline, size: 16),
                    label: const Text('Details'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.teal,
                      side: const BorderSide(color: Colors.teal),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _bookAppointment(therapist),
                    icon: const Icon(Icons.calendar_today, size: 16),
                    label: const Text('Book'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.teal,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showTherapistDetails(Map<String, dynamic> therapist) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(therapist['name'] ?? 'Therapist Details'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Specialty: ${therapist['specialty'] ?? 'General'}'),
            const SizedBox(height: 8),
            Text('Rating: ${therapist['rating'] ?? 'N/A'} ⭐'),
            const SizedBox(height: 8),
            Text(
                'Experience: ${therapist['experience'] ?? 'Not specified'} years'),
            const SizedBox(height: 8),
            Text('Available: ${therapist['available'] ?? 'Yes'}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _bookAppointment(therapist);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.teal),
            child: const Text('Book Appointment'),
          ),
        ],
      ),
    );
  }

  void _bookAppointment(Map<String, dynamic> therapist) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Booking appointment with ${therapist['name']}...'),
        backgroundColor: Colors.teal,
        action: SnackBarAction(
          label: 'View',
          textColor: Colors.white,
          onPressed: () {
            // Navigate to booking screen
          },
        ),
      ),
    );
  }
}

// Custom painter for map grid
class MapGridPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.grey.withOpacity(0.2)
      ..strokeWidth = 1;

    // Draw grid lines
    for (int i = 0; i < size.width; i += 20) {
      canvas.drawLine(
        Offset(i.toDouble(), 0),
        Offset(i.toDouble(), size.height),
        paint,
      );
    }

    for (int i = 0; i < size.height; i += 20) {
      canvas.drawLine(
        Offset(0, i.toDouble()),
        Offset(size.width, i.toDouble()),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
