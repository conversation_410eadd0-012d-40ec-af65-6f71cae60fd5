import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;

class SemanticAnalysisScreen extends StatefulWidget {
  final String userId;

  const SemanticAnalysisScreen({super.key, required this.userId});

  @override
  _SemanticAnalysisScreenState createState() => _SemanticAnalysisScreenState();
}

class _SemanticAnalysisScreenState extends State<SemanticAnalysisScreen> {
  final TextEditingController _textController = TextEditingController();
  Map<String, dynamic>? analysisResult;
  List<Map<String, dynamic>> analysisHistory = [];
  bool isAnalyzing = false;
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    _fetchAnalysisHistory();
  }

  Future<void> _fetchAnalysisHistory() async {
    setState(() {
      isLoading = true;
    });

    try {
      final response = await http.get(
        Uri.parse('http://localhost:3000/api/semantic-analysis/${widget.userId}'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        setState(() {
          analysisHistory = List<Map<String, dynamic>>.from(data['analyses'] ?? []);
        });
      }
    } catch (e) {
      print('Error fetching analysis history: $e');
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  Future<void> _analyzeText() async {
    if (_textController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter some text to analyze')),
      );
      return;
    }

    setState(() {
      isAnalyzing = true;
    });

    try {
      // Simulate AI analysis
      await Future.delayed(const Duration(seconds: 2));

      final text = _textController.text.trim();
      final analysis = _performSemanticAnalysis(text);

      // Save analysis to backend
      final response = await http.post(
        Uri.parse('http://localhost:3000/api/semantic-analysis'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'userId': widget.userId,
          'text': text,
          'analysis': analysis,
          'date': DateTime.now().toIso8601String(),
        }),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        setState(() {
          analysisResult = analysis;
        });
        _fetchAnalysisHistory();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Analysis completed successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        throw Exception('Failed to save analysis');
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $e')),
      );
    } finally {
      setState(() {
        isAnalyzing = false;
      });
    }
  }

  Map<String, dynamic> _performSemanticAnalysis(String text) {
    // Simulate AI-powered semantic analysis
    final words = text.toLowerCase().split(' ');
    
    // Sentiment analysis
    final positiveWords = ['happy', 'good', 'great', 'excellent', 'wonderful', 'amazing', 'love', 'joy', 'excited', 'grateful'];
    final negativeWords = ['sad', 'bad', 'terrible', 'awful', 'hate', 'angry', 'depressed', 'anxious', 'worried', 'stressed'];
    
    int positiveCount = 0;
    int negativeCount = 0;
    
    for (String word in words) {
      if (positiveWords.contains(word)) positiveCount++;
      if (negativeWords.contains(word)) negativeCount++;
    }
    
    double sentimentScore = (positiveCount - negativeCount) / words.length;
    String sentiment = sentimentScore > 0.1 ? 'Positive' : 
                     sentimentScore < -0.1 ? 'Negative' : 'Neutral';
    
    // Emotion detection
    List<Map<String, dynamic>> emotions = [
      {'emotion': 'Joy', 'confidence': (positiveCount / words.length * 100).clamp(0, 100)},
      {'emotion': 'Sadness', 'confidence': (negativeCount / words.length * 100).clamp(0, 100)},
      {'emotion': 'Anxiety', 'confidence': (words.where((w) => ['worried', 'anxious', 'nervous'].contains(w)).length / words.length * 100).clamp(0, 100)},
      {'emotion': 'Anger', 'confidence': (words.where((w) => ['angry', 'mad', 'furious'].contains(w)).length / words.length * 100).clamp(0, 100)},
    ];
    
    emotions.sort((a, b) => b['confidence'].compareTo(a['confidence']));
    
    // Key themes
    List<String> themes = [];
    if (words.any((w) => ['work', 'job', 'career', 'office'].contains(w))) themes.add('Work/Career');
    if (words.any((w) => ['family', 'parents', 'children', 'spouse'].contains(w))) themes.add('Family');
    if (words.any((w) => ['friends', 'social', 'relationship'].contains(w))) themes.add('Relationships');
    if (words.any((w) => ['health', 'sick', 'doctor', 'medicine'].contains(w))) themes.add('Health');
    if (words.any((w) => ['money', 'financial', 'bills', 'debt'].contains(w))) themes.add('Financial');
    
    // Recommendations
    List<String> recommendations = [];
    if (sentiment == 'Negative') {
      recommendations.add('Consider practicing mindfulness or meditation');
      recommendations.add('Reach out to a mental health professional');
      recommendations.add('Try journaling to process your emotions');
    } else if (sentiment == 'Positive') {
      recommendations.add('Keep up the positive mindset!');
      recommendations.add('Share your joy with others');
      recommendations.add('Consider what contributed to these positive feelings');
    } else {
      recommendations.add('Reflect on your current emotional state');
      recommendations.add('Consider setting small goals for improvement');
      recommendations.add('Practice gratitude exercises');
    }
    
    return {
      'sentiment': sentiment,
      'sentimentScore': sentimentScore,
      'emotions': emotions,
      'themes': themes,
      'recommendations': recommendations,
      'wordCount': words.length,
      'analysisDate': DateTime.now().toIso8601String(),
    };
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('AI Semantic Analysis'),
        backgroundColor: Colors.indigo,
      ),
      body: DefaultTabController(
        length: 2,
        child: Column(
          children: [
            const TabBar(
              labelColor: Colors.indigo,
              tabs: [
                Tab(text: 'Analyze Text', icon: Icon(Icons.analytics)),
                Tab(text: 'History', icon: Icon(Icons.history)),
              ],
            ),
            Expanded(
              child: TabBarView(
                children: [
                  _buildAnalysisTab(),
                  _buildHistoryTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnalysisTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'AI-Powered Text Analysis',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Enter your thoughts, journal entries, or any text for AI analysis',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 20),

          // Text input
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Enter Text to Analyze:',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 12),
                  TextField(
                    controller: _textController,
                    maxLines: 8,
                    decoration: const InputDecoration(
                      hintText: 'Type or paste your text here...\n\nExample: "I had a really challenging day at work today. The project deadline is approaching and I feel overwhelmed, but I\'m grateful for my supportive team."',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 16),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: isAnalyzing ? null : _analyzeText,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.indigo,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: isAnalyzing
                          ? const Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    color: Colors.white,
                                    strokeWidth: 2,
                                  ),
                                ),
                                SizedBox(width: 12),
                                Text('Analyzing...'),
                              ],
                            )
                          : const Text(
                              'Analyze Text',
                              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                            ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),

          // Analysis results
          if (analysisResult != null) _buildAnalysisResults(),
        ],
      ),
    );
  }

  Widget _buildAnalysisResults() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Analysis Results',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),

        // Sentiment
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Overall Sentiment',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: _getSentimentColor(analysisResult!['sentiment']).withOpacity(0.2),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(color: _getSentimentColor(analysisResult!['sentiment'])),
                      ),
                      child: Text(
                        analysisResult!['sentiment'],
                        style: TextStyle(
                          color: _getSentimentColor(analysisResult!['sentiment']),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text('Score: ${(analysisResult!['sentimentScore'] * 100).toStringAsFixed(1)}%'),
                  ],
                ),
              ],
            ),
          ),
        ),

        // Emotions
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Detected Emotions',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 12),
                ...analysisResult!['emotions'].take(3).map<Widget>((emotion) =>
                  Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: Text(emotion['emotion']),
                        ),
                        Expanded(
                          flex: 3,
                          child: LinearProgressIndicator(
                            value: emotion['confidence'] / 100,
                            backgroundColor: Colors.grey[300],
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.indigo),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text('${emotion['confidence'].toStringAsFixed(1)}%'),
                      ],
                    ),
                  ),
                ).toList(),
              ],
            ),
          ),
        ),

        // Themes
        if (analysisResult!['themes'].isNotEmpty)
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Key Themes',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: analysisResult!['themes'].map<Widget>((theme) =>
                      Chip(
                        label: Text(theme),
                        backgroundColor: Colors.indigo.withOpacity(0.1),
                      ),
                    ).toList(),
                  ),
                ],
              ),
            ),
          ),

        // Recommendations
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'AI Recommendations',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 12),
                ...analysisResult!['recommendations'].map<Widget>((rec) =>
                  Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Icon(Icons.lightbulb, color: Colors.amber, size: 20),
                        const SizedBox(width: 8),
                        Expanded(child: Text(rec)),
                      ],
                    ),
                  ),
                ).toList(),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Color _getSentimentColor(String sentiment) {
    switch (sentiment) {
      case 'Positive':
        return Colors.green;
      case 'Negative':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Widget _buildHistoryTab() {
    if (isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (analysisHistory.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.analytics, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No analysis history',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            SizedBox(height: 8),
            Text(
              'Analyze some text to see your history here',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: analysisHistory.length,
      itemBuilder: (context, index) {
        final analysis = analysisHistory[index];
        final date = DateTime.parse(analysis['date']);
        
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ExpansionTile(
            leading: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: _getSentimentColor(analysis['analysis']['sentiment']).withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                analysis['analysis']['sentiment'],
                style: TextStyle(
                  color: _getSentimentColor(analysis['analysis']['sentiment']),
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            title: Text(
              analysis['text'].length > 50 
                  ? '${analysis['text'].substring(0, 50)}...'
                  : analysis['text'],
            ),
            subtitle: Text(
              '${date.day}/${date.month}/${date.year} at ${date.hour}:${date.minute.toString().padLeft(2, '0')}',
            ),
            children: [
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Full Text:',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text(analysis['text']),
                    const SizedBox(height: 16),
                    Text(
                      'Analysis Summary:',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text('Sentiment: ${analysis['analysis']['sentiment']}'),
                    Text('Word Count: ${analysis['analysis']['wordCount']}'),
                    if (analysis['analysis']['themes'].isNotEmpty)
                      Text('Themes: ${analysis['analysis']['themes'].join(', ')}'),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }
}
