import 'package:flutter/material.dart';
import 'package:myapp/api_service.dart';
import 'package:myapp/forget-password.dart';
import 'package:myapp/screens/beautiful_home_screen.dart';
import 'package:myapp/signup_page.dart';
import 'package:myapp/therapist_dashboard.dart';
import 'package:myapp/test_therapist_login.dart';
import 'package:myapp/mock_api_service.dart';
import 'package:myapp/services/therapist_logger.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  _LoginPageState createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool isPasswordVisible = false;
  String? selectedRole;

  @override
  void dispose() {
    emailController.dispose();
    passwordController.dispose();
    super.dispose();
  }

  Future<void> login(BuildContext context) async {
    if (selectedRole == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text("Please select a role")),
      );
      return;
    }

    if (!_formKey.currentState!.validate()) {
      return;
    }

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(child: CircularProgressIndicator()),
    );

    final email = emailController.text.trim();
    final password = passwordController.text.trim();

    try {
      final userType = selectedRole == "user" ? "user" : "therapist";

      // Check for demo credentials first
      if (_isDemoCredentials(email, password, userType)) {
        if (mounted) Navigator.pop(context);
        await _handleDemoLogin(context, userType);
        return;
      }

      final result = await ApiService.login(email, password, userType);

      if (mounted) Navigator.pop(context);

      if (result['success']) {
        final userData = result['data'];
        final userId = userData['user']['_id'];

        SharedPreferences prefs = await SharedPreferences.getInstance();
        await prefs.setString('userId', userId);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text("Login successful!"),
              backgroundColor: Colors.green,
            ),
          );

          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => userType == 'user'
                  ? BeautifulHomeScreen(userId: userId)
                  : TherapistDashboard(therapistId: userId),
            ),
          );
        }
      } else {
        // Try demo login as fallback
        if (_isDemoCredentials(email, password, userType)) {
          await _handleDemoLogin(context, userType);
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text("Login failed: ${result['error']}"),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      }
    } catch (error) {
      if (mounted) {
        Navigator.pop(context);

        // Try demo login as fallback for any error
        if (_isDemoCredentials(
            email, password, selectedRole == "user" ? "user" : "therapist")) {
          await _handleDemoLogin(
              context, selectedRole == "user" ? "user" : "therapist");
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text("An error occurred: $error")),
          );
        }
      }
    }
  }

  String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return "Email is required";
    }
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(value)) {
      return "Enter a valid email";
    }
    return null;
  }

  String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return "Password is required";
    }
    if (value.length < 6) {
      return "Password must be at least 6 characters";
    }
    return null;
  }

  bool _isDemoCredentials(String email, String password, String userType) {
    // Demo credentials for testing
    final demoCredentials = {
      'therapist': {
        '<EMAIL>': 'password123',
        '<EMAIL>': 'demo123',
        '<EMAIL>': 'demo123',
      },
      'user': {
        '<EMAIL>': 'password123',
        '<EMAIL>': 'demo123',
      },
    };

    return demoCredentials[userType]?.containsKey(email) == true &&
        demoCredentials[userType]![email] == password;
  }

  Future<void> _handleDemoLogin(BuildContext context, String userType) async {
    // Generate demo user ID
    final demoUserId =
        userType == 'therapist' ? 'test-therapist-id' : 'test-user-id';

    // Log therapist login if it's a therapist
    if (userType == 'therapist') {
      final logger = TherapistLogger();
      logger.initialize(demoUserId);
      logger.logLogin(demoUserId, success: true);
    }

    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString('userId', demoUserId);
    await prefs.setString('userType', userType);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text("Demo login successful! 🎉"),
          backgroundColor: Colors.green,
        ),
      );

      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => userType == 'user'
              ? BeautifulHomeScreen(userId: demoUserId)
              : TherapistDashboard(therapistId: demoUserId),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF667eea),
              Color(0xFF764ba2),
            ],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
            child: Form(
              key: _formKey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  SizedBox(height: MediaQuery.of(context).size.height * 0.02),
                  _header(),
                  const SizedBox(height: 24),
                  _inputField(),
                  const SizedBox(height: 16),
                  _roleSelector(),
                  const SizedBox(height: 16),
                  _forgotPasswordButton(),
                  const SizedBox(height: 8),
                  _loginButton(context),
                  const SizedBox(height: 16),
                  _signup(),
                  SizedBox(height: MediaQuery.of(context).size.height * 0.02),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _header() {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 1000),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 50 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: const Icon(
                    Icons.psychology,
                    size: 60,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 24),
                const Text(
                  "Welcome Back!",
                  style: TextStyle(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    letterSpacing: 1.2,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  "Sign in to continue your mental wellness journey",
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white.withOpacity(0.9),
                    fontWeight: FontWeight.w400,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _inputField() {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 800),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 30 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.95),
                borderRadius: BorderRadius.circular(24),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  TextFormField(
                    controller: emailController,
                    decoration: InputDecoration(
                      hintText: "Email Address",
                      prefixIcon: const Icon(
                        Icons.email_outlined,
                        color: Color(0xFF8B5CF6),
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(16),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: const Color(0xFFF8FAFC),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 16,
                      ),
                    ),
                    validator: validateEmail,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: passwordController,
                    obscureText: !isPasswordVisible,
                    decoration: InputDecoration(
                      hintText: "Password",
                      prefixIcon: const Icon(
                        Icons.lock_outline,
                        color: Color(0xFF8B5CF6),
                      ),
                      suffixIcon: IconButton(
                        icon: Icon(
                          isPasswordVisible
                              ? Icons.visibility_outlined
                              : Icons.visibility_off_outlined,
                          color: const Color(0xFF8B5CF6),
                        ),
                        onPressed: () {
                          setState(() {
                            isPasswordVisible = !isPasswordVisible;
                          });
                        },
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(16),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: const Color(0xFFF8FAFC),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 16,
                      ),
                    ),
                    validator: validatePassword,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _forgotPasswordButton() {
    return TextButton(
      onPressed: () {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => ForgotPasswordPage()),
        );
      },
      child: Text(
        "Forgot Password?",
        style: TextStyle(
          color: Colors.white.withOpacity(0.9),
          fontWeight: FontWeight.w600,
          fontSize: 14,
        ),
      ),
    );
  }

  Widget _loginButton(BuildContext context) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 600),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.scale(
          scale: 0.8 + (0.2 * value),
          child: Opacity(
            opacity: value,
            child: Container(
              width: double.infinity,
              height: 56,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [
                    Color(0xFF8B5CF6),
                    Color(0xFFA855F7),
                  ],
                ),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF8B5CF6).withOpacity(0.4),
                    blurRadius: 15,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: ElevatedButton(
                onPressed: () async {
                  await login(context);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.transparent,
                  shadowColor: Colors.transparent,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
                child: const Text(
                  "Sign In",
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                    letterSpacing: 0.5,
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _signup() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          "Don't have an account? ",
          style: TextStyle(
            color: Colors.white.withOpacity(0.8),
            fontSize: 14,
          ),
        ),
        TextButton(
          onPressed: () {
            Navigator.push(
                context, MaterialPageRoute(builder: (context) => SignupPage()));
          },
          child: const Text(
            "Sign Up",
            style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
        ),
        // Debug button for testing therapist login
        TextButton(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const TestTherapistLogin(),
              ),
            );
          },
          child: const Text(
            "🔧 Test Therapist Login",
            style: TextStyle(
              color: Colors.orange,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  Widget _roleSelector() {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 700),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 20 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.9),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    "Select Your Role",
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                      color: Color(0xFF1F2937),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: GestureDetector(
                          onTap: () {
                            setState(() {
                              selectedRole = "user";
                            });
                          },
                          child: Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: selectedRole == "user"
                                  ? const Color(0xFF8B5CF6).withOpacity(0.1)
                                  : Colors.grey.shade50,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: selectedRole == "user"
                                    ? const Color(0xFF8B5CF6)
                                    : Colors.grey.shade300,
                                width: 2,
                              ),
                            ),
                            child: Column(
                              children: [
                                Icon(
                                  Icons.person_outline,
                                  color: selectedRole == "user"
                                      ? const Color(0xFF8B5CF6)
                                      : Colors.grey.shade600,
                                  size: 32,
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  "User",
                                  style: TextStyle(
                                    fontWeight: FontWeight.w600,
                                    color: selectedRole == "user"
                                        ? const Color(0xFF8B5CF6)
                                        : Colors.grey.shade700,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: GestureDetector(
                          onTap: () {
                            setState(() {
                              selectedRole = "therapist";
                            });
                          },
                          child: Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: selectedRole == "therapist"
                                  ? const Color(0xFF8B5CF6).withOpacity(0.1)
                                  : Colors.grey.shade50,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: selectedRole == "therapist"
                                    ? const Color(0xFF8B5CF6)
                                    : Colors.grey.shade300,
                                width: 2,
                              ),
                            ),
                            child: Column(
                              children: [
                                Icon(
                                  Icons.medical_services_outlined,
                                  color: selectedRole == "therapist"
                                      ? const Color(0xFF8B5CF6)
                                      : Colors.grey.shade600,
                                  size: 32,
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  "Therapist",
                                  style: TextStyle(
                                    fontWeight: FontWeight.w600,
                                    color: selectedRole == "therapist"
                                        ? const Color(0xFF8B5CF6)
                                        : Colors.grey.shade700,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  if (selectedRole == null)
                    Padding(
                      padding: const EdgeInsets.only(top: 12.0),
                      child: Text(
                        "Please select a role to continue",
                        style: TextStyle(
                          color: Colors.red.shade600,
                          fontSize: 12,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}



// import 'dart:convert';
// import 'package:flutter/material.dart';
// import 'package:http/http.dart' as http;
// import 'package:myapp/forget-password.dart';
// import 'package:myapp/home_page.dart';
// import 'package:myapp/signup_page.dart';
// import 'package:myapp/therapist_dashboard.dart'; // Import the therapist dashboard

// class LoginPage extends StatefulWidget {
//   const LoginPage({super.key});

//   @override
//   _LoginPageState createState() => _LoginPageState();
// }

// class _LoginPageState extends State<LoginPage> {
//   final emailController = TextEditingController();
//   final passwordController = TextEditingController();
//   final _formKey = GlobalKey<FormState>();
//   bool isPasswordVisible = false;
//   bool isLoading = false;

//   @override
//   void dispose() {
//     emailController.dispose();
//     passwordController.dispose();
//     super.dispose();
//   }

//   Future<void> login(BuildContext context) async {
//     if (!_formKey.currentState!.validate()) {
//       return;
//     }

//     setState(() {
//       isLoading = true;
//     });

//     showDialog(
//       context: context,
//       barrierDismissible: false,
//       builder: (context) => const Center(child: CircularProgressIndicator()),
//     );

//     final email = emailController.text.trim();
//     final password = passwordController.text.trim();

//     try {
//       final response = await http.post(
//         Uri.parse("http://localhost:3000/api/users/login"),
//         headers: {"Content-Type": "application/json"},
//         body: jsonEncode({"email": email, "password": password}),
//       );

//       // Close the loading dialog
//       Navigator.pop(context);
      
//       if (response.statusCode == 200) {
//         final responseData = jsonDecode(response.body);
//         final user = responseData['user'];
//         final userRole = user['role'] ?? 'user'; // Default to 'user' if role not specified
        
//         if (userRole == 'therapist') {
//           // Navigate to therapist dashboard
//           Navigator.pushReplacement(
//             context,
//             MaterialPageRoute(
//               builder: (context) => TherapistDashboard(therapistId: user['_id']),
//             ),
//           );
//         } else {
//           // Navigate to user home screen
//           Navigator.pushReplacement(
//             context,
//             MaterialPageRoute(
//               builder: (context) => HomeScreen(userId: user['_id']),
//             ),
//           );
//         }
//       } else {
//         // Try therapist login if user login fails
//         await tryTherapistLogin(email, password);
//       }
//     } catch (error) {
//       Navigator.pop(context);
//       ScaffoldMessenger.of(context).showSnackBar(
//         SnackBar(content: Text("An error occurred: $error")),
//       );
//     } finally {
//       setState(() {
//         isLoading = false;
//       });
//     }
//   }
  
//   Future<void> tryTherapistLogin(String email, String password) async {
//     try {
//       final response = await http.post(
//         Uri.parse("http://localhost:3000/api/therapists/login"),
//         headers: {"Content-Type": "application/json"},
//         body: jsonEncode({"email": email, "password": password}),
//       );
      
//       if (response.statusCode == 200) {
//         final responseData = jsonDecode(response.body);
//         // Navigate to therapist dashboard
//         Navigator.pushReplacement(
//           context,
//           MaterialPageRoute(
//             builder: (context) => TherapistDashboard(therapistId: responseData['therapist']['_id']),
//           ),
//         );
//       } else {
//         // Both login attempts failed
//         ScaffoldMessenger.of(context).showSnackBar(
//           const SnackBar(content: Text("Invalid email or password")),
//         );
//       }
//     } catch (error) {
//       ScaffoldMessenger.of(context).showSnackBar(
//         SnackBar(content: Text("An error occurred: $error")),
//       );
//     }
//   }

//   String? validateEmail(String? value) {
//     if (value == null || value.isEmpty) {
//       return "Email is required";
//     }
//     final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
//     if (!emailRegex.hasMatch(value)) {
//       return "Enter a valid email";
//     }
//     return null;
//   }

//   String? validatePassword(String? value) {
//     if (value == null || value.isEmpty) {
//       return "Password is required";
//     }
//     if (value.length < 6) {
//       return "Password must be at least 6 characters";
//     }
//     return null;
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       body: Container(
//         decoration: const BoxDecoration(
//           gradient: LinearGradient(
//             begin: Alignment.topLeft,
//             end: Alignment.bottomRight,
//             colors: [Color(0xFFFFE29F), Color(0xFFFFC0CB)],
//           ),
//         ),
//         child: Padding(
//           padding: const EdgeInsets.symmetric(horizontal: 24),
//           child: Form(
//             key: _formKey,
//             child: Column(
//               mainAxisAlignment: MainAxisAlignment.center,
//               crossAxisAlignment: CrossAxisAlignment.stretch,
//               children: [
//                 _header(),
//                 const SizedBox(height: 30),
//                 _inputField(),
//                 const SizedBox(height: 20),
//                 _forgotPasswordButton(),
//                 const SizedBox(height: 10),
//                 _loginButton(context),
//                 const SizedBox(height: 20),
//                 _signup(),
//               ],
//             ),
//           ),
//         ),
//       ),
//     );
//   }

//   Widget _header() {
//     return Column(
//       children: const [
//         Text(
//           "Welcome Back!",
//           style: TextStyle(fontSize: 36, fontWeight: FontWeight.bold, color: Colors.black),
//           textAlign: TextAlign.center,
//         ),
//         SizedBox(height: 10),
//         Text(
//           "Login to continue",
//           style: TextStyle(fontSize: 16, color: Colors.black54),
//           textAlign: TextAlign.center,
//         ),
//       ],
//     );
//   }

//   Widget _inputField() {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.stretch,
//       children: [
//         TextFormField(
//           controller: emailController,
//           decoration: const InputDecoration(
//             hintText: "Email",
//             prefixIcon: Icon(Icons.email, color: Colors.purple),
//           ),
//           validator: validateEmail,
//         ),
//         const SizedBox(height: 10),
//         TextFormField(
//           controller: passwordController,
//           obscureText: !isPasswordVisible,
//           decoration: InputDecoration(
//             hintText: "Password",
//             prefixIcon: const Icon(Icons.lock, color: Colors.purple),
//             suffixIcon: IconButton(
//               icon: Icon(
//                 isPasswordVisible ? Icons.visibility : Icons.visibility_off,
//                 color: Colors.purple,
//               ),
//               onPressed: () {
//                 setState(() {
//                   isPasswordVisible = !isPasswordVisible;
//                 });
//               },
//             ),
//           ),
//           validator: validatePassword,
//         ),
//       ],
//     );
//   }

//   Widget _forgotPasswordButton() {
//     return TextButton(
//       onPressed: () {
//         Navigator.push(
//           context,
//           MaterialPageRoute(builder: (context) => ForgotPasswordScreen()),
//         );
//       },
//       child: const Text(
//         "Forgot Password?",
//         style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
//       ),
//     );
//   }

//   Widget _loginButton(BuildContext context) {
//     return ElevatedButton(
//       onPressed: isLoading ? null : () async {
//         await login(context);
//       },
//       style: ElevatedButton.styleFrom(
//         shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(30)),
//         padding: const EdgeInsets.symmetric(vertical: 16),
//         backgroundColor: Colors.deepPurple,
//         disabledBackgroundColor: Colors.deepPurple.withOpacity(0.5),
//       ),
//       child: isLoading 
//         ? const SizedBox(
//             height: 20,
//             width: 20,
//             child: CircularProgressIndicator(color: Colors.white, strokeWidth: 2),
//           )
//         : const Text(
//             "Login",
//             style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Colors.white),
//           ),
//     );
//   }

//   Widget _signup() {
//     return Row(
//       mainAxisAlignment: MainAxisAlignment.center,
//       children: [
//         const Text("Don't have an account? ", style: TextStyle(color: Colors.black54)),
//         TextButton(
//           onPressed: () {
//             Navigator.push(context, MaterialPageRoute(builder: (context) => SignupPage()));
//           },
//           child: const Text("Sign Up", style: TextStyle(color: Colors.purple, fontWeight: FontWeight.bold)),
//         ),
//       ],
//     );
//   }
// }