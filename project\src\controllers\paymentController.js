const { Payment, User } = require('../models');

// Initialize Stripe with fallback for development
let stripe;
try {
  const stripeKey = process.env.STRIPE_SECRET_KEY || 'sk_test_demo_key';
  if (stripeKey === 'sk_test_demo_key') {
    console.warn('⚠️  Using demo Stripe key. Please set STRIPE_SECRET_KEY in .env file for production.');
  }
  stripe = require('stripe')(stripeKey);
} catch (error) {
  console.error('Failed to initialize Stripe:', error.message);
  stripe = null;
}

class PaymentController {
  static async getAllPayments(req, res, next) {
    try {
      const payments = await Payment.find().populate('userId', '-passwordHash');
      res.json(payments);
    } catch (error) {
      next(error);
    }
  }

  static async getPaymentById(req, res, next) {
    try {
      const payment = await Payment.findById(req.params.id)
        .populate('userId', '-passwordHash');

      if (!payment) {
        return res.status(404).json({ message: 'Payment not found' });
      }

      res.json(payment);
    } catch (error) {
      next(error);
    }
  }

  // Create payment intent for Stripe
  static async createPaymentIntent(req, res, next) {
    try {
      const { amount, currency = 'usd', userId, appointmentId, description } = req.body;

      if (!amount || !userId) {
        return res.status(400).json({ error: 'Amount and user ID are required' });
      }

      if (!stripe) {
        return res.status(503).json({ error: 'Payment service unavailable. Please configure Stripe.' });
      }

      // Create payment intent with Stripe
      const paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(amount), // Amount in cents
        currency: currency,
        metadata: {
          userId: userId,
          appointmentId: appointmentId || '',
          description: description || 'Therapy session payment'
        },
        automatic_payment_methods: {
          enabled: true,
        },
      });

      // Save payment record in database
      const payment = new Payment({
        userId,
        appointmentId,
        amount: amount / 100, // Convert back to dollars
        currency,
        stripePaymentIntentId: paymentIntent.id,
        paymentDate: new Date(),
        transactionStatus: 'pending',
        description: description || 'Therapy session payment'
      });

      await payment.save();

      res.status(201).json({
        id: paymentIntent.id,
        clientSecret: paymentIntent.client_secret,
        amount: paymentIntent.amount,
        currency: paymentIntent.currency,
        paymentId: payment._id
      });
    } catch (error) {
      console.error('Error creating payment intent:', error);
      next(error);
    }
  }

  // Confirm payment
  static async confirmPayment(req, res, next) {
    try {
      const { paymentIntentId, userId } = req.body;

      if (!paymentIntentId || !userId) {
        return res.status(400).json({ error: 'Payment intent ID and user ID are required' });
      }

      // Retrieve payment intent from Stripe
      const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);

      // Update payment record in database
      const payment = await Payment.findOneAndUpdate(
        { stripePaymentIntentId: paymentIntentId },
        {
          transactionStatus: paymentIntent.status === 'succeeded' ? 'completed' : 'failed',
          completedAt: paymentIntent.status === 'succeeded' ? new Date() : null
        },
        { new: true }
      );

      if (!payment) {
        return res.status(404).json({ error: 'Payment record not found' });
      }

      res.json({
        success: paymentIntent.status === 'succeeded',
        status: paymentIntent.status,
        payment: payment
      });
    } catch (error) {
      console.error('Error confirming payment:', error);
      next(error);
    }
  }

  // Get payment history for user
  static async getPaymentHistory(req, res, next) {
    try {
      const { userId } = req.params;

      const payments = await Payment.find({ userId })
        .sort({ paymentDate: -1 })
        .populate('appointmentId', 'appointmentDate therapistId')
        .limit(50);

      res.json(payments);
    } catch (error) {
      next(error);
    }
  }

  // Get appointment pricing
  static async getAppointmentPricing(req, res, next) {
    try {
      const { therapistId, type, duration } = req.query;

      if (!therapistId || !type || !duration) {
        return res.status(400).json({ error: 'Therapist ID, type, and duration are required' });
      }

      // Mock pricing logic - replace with actual pricing from database
      const basePrices = {
        'online': 80,
        'physical': 120
      };

      const durationMultiplier = {
        30: 0.6,
        60: 1.0,
        90: 1.4
      };

      const basePrice = basePrices[type] || basePrices['online'];
      const multiplier = durationMultiplier[duration] || 1.0;
      const sessionFee = basePrice * multiplier;
      const serviceFee = 2.50;
      const taxRate = 0.08;
      const tax = sessionFee * taxRate;
      const total = sessionFee + serviceFee + tax;

      res.json({
        sessionFee: sessionFee,
        serviceFee: serviceFee,
        tax: tax,
        total: total,
        currency: 'USD',
        duration: duration,
        type: type
      });
    } catch (error) {
      next(error);
    }
  }

  // Process refund
  static async processRefund(req, res, next) {
    try {
      const { paymentIntentId, userId, amount, reason } = req.body;

      if (!paymentIntentId || !userId) {
        return res.status(400).json({ error: 'Payment intent ID and user ID are required' });
      }

      // Find payment record
      const payment = await Payment.findOne({ stripePaymentIntentId: paymentIntentId });
      if (!payment) {
        return res.status(404).json({ error: 'Payment not found' });
      }

      // Create refund with Stripe
      const refund = await stripe.refunds.create({
        payment_intent: paymentIntentId,
        amount: amount ? Math.round(amount) : undefined, // Partial or full refund
        reason: reason || 'requested_by_customer'
      });

      // Update payment record
      payment.transactionStatus = 'refunded';
      payment.refundId = refund.id;
      payment.refundAmount = refund.amount / 100;
      payment.refundDate = new Date();
      payment.refundReason = reason;
      await payment.save();

      res.json({
        success: true,
        refund: refund,
        payment: payment
      });
    } catch (error) {
      console.error('Error processing refund:', error);
      next(error);
    }
  }

  // Save payment method
  static async savePaymentMethod(req, res, next) {
    try {
      const { userId, paymentMethodId } = req.body;

      if (!userId || !paymentMethodId) {
        return res.status(400).json({ error: 'User ID and payment method ID are required' });
      }

      // Attach payment method to customer in Stripe
      const user = await User.findById(userId);
      if (!user.stripeCustomerId) {
        // Create Stripe customer if doesn't exist
        const customer = await stripe.customers.create({
          email: user.email,
          name: user.name,
          metadata: { userId: userId }
        });
        user.stripeCustomerId = customer.id;
        await user.save();
      }

      await stripe.paymentMethods.attach(paymentMethodId, {
        customer: user.stripeCustomerId,
      });

      res.json({ success: true, message: 'Payment method saved successfully' });
    } catch (error) {
      console.error('Error saving payment method:', error);
      next(error);
    }
  }

  // Get saved payment methods
  static async getSavedPaymentMethods(req, res, next) {
    try {
      const { userId } = req.params;

      const user = await User.findById(userId);
      if (!user || !user.stripeCustomerId) {
        return res.json([]);
      }

      const paymentMethods = await stripe.paymentMethods.list({
        customer: user.stripeCustomerId,
        type: 'card',
      });

      const formattedMethods = paymentMethods.data.map(pm => ({
        id: pm.id,
        brand: pm.card.brand,
        last4: pm.card.last4,
        expMonth: pm.card.exp_month,
        expYear: pm.card.exp_year,
        funding: pm.card.funding
      }));

      res.json(formattedMethods);
    } catch (error) {
      console.error('Error getting payment methods:', error);
      next(error);
    }
  }

  // Delete payment method
  static async deletePaymentMethod(req, res, next) {
    try {
      const { paymentMethodId } = req.params;
      const { userId } = req.body;

      if (!userId) {
        return res.status(400).json({ error: 'User ID is required' });
      }

      await stripe.paymentMethods.detach(paymentMethodId);

      res.json({ success: true, message: 'Payment method deleted successfully' });
    } catch (error) {
      console.error('Error deleting payment method:', error);
      next(error);
    }
  }

  // Get payment status
  static async getPaymentStatus(req, res, next) {
    try {
      const { paymentIntentId } = req.params;

      const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);

      res.json({
        status: paymentIntent.status,
        amount: paymentIntent.amount,
        currency: paymentIntent.currency,
        created: paymentIntent.created
      });
    } catch (error) {
      console.error('Error getting payment status:', error);
      next(error);
    }
  }

  static async createPayment(req, res, next) {
    try {
      const { userId, amount } = req.body;

      const payment = new Payment({
        userId,
        amount,
        paymentDate: new Date(),
        transactionStatus: 'Pending',
      });

      await payment.save();

      // Update user's payment references
      await User.findByIdAndUpdate(userId, {
        $push: { paymentIds: payment._id },
      });

      res.status(201).json(payment);
    } catch (error) {
      next(error);
    }
  }

  static async updatePayment(req, res, next) {
    try {
      const { transactionStatus } = req.body;

      const payment = await Payment.findByIdAndUpdate(
        req.params.id,
        { transactionStatus },
        { new: true }
      );

      if (!payment) {
        return res.status(404).json({ message: 'Payment not found' });
      }

      res.json(payment);
    } catch (error) {
      next(error);
    }
  }

  static async getUserPayments(req, res, next) {
    try {
      const payments = await Payment.find({ userId: req.params.userId })
        .sort({ paymentDate: -1 });

      res.json(payments);
    } catch (error) {
      next(error);
    }
  }
}

module.exports = PaymentController;