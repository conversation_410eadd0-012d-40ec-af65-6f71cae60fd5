!function(t){function i(t,_,r,e){return this instanceof i?(this.remainder=null,"string"==typeof t?h.call(this,t,_):"undefined"==typeof _?s.call(this,t):void a.apply(this,arguments)):new i(t,_,r,e)}function a(t,i,a,s){return"undefined"==typeof a?(this._a00=65535&t,this._a16=t>>>16,this._a32=65535&i,this._a48=i>>>16,this):(this._a00=0|t,this._a16=0|i,this._a32=0|a,this._a48=0|s,this)}function s(t){return this._a00=65535&t,this._a16=t>>>16,this._a32=0,this._a48=0,this}function h(t,a){a=a||10,this._a00=0,this._a16=0,this._a32=0,this._a48=0;for(var s=_[a]||new i(Math.pow(a,5)),h=0,r=t.length;r>h;h+=5){var e=Math.min(5,r-h),n=parseInt(t.slice(h,h+e),a);this.multiply(5>e?new i(Math.pow(a,e)):s).add(new i(n))}return this}var _={16:i(Math.pow(16,5)),10:i(Math.pow(10,5)),2:i(Math.pow(2,5))},r={16:i(16),10:i(10),2:i(2)};i.prototype.fromBits=a,i.prototype.fromNumber=s,i.prototype.fromString=h,i.prototype.toNumber=function(){return 65536*this._a16+this._a00},i.prototype.toString=function(t){t=t||10;var a=r[t]||new i(t);if(!this.gt(a))return this.toNumber().toString(t);for(var s=this.clone(),h=new Array(64),_=63;_>=0&&(s.div(a),h[_]=s.remainder.toNumber().toString(t),s.gt(a));_--);return h[_-1]=s.toNumber().toString(t),h.join("")},i.prototype.add=function(t){var i=this._a00+t._a00,a=i>>>16;a+=this._a16+t._a16;var s=a>>>16;s+=this._a32+t._a32;var h=s>>>16;return h+=this._a48+t._a48,this._a00=65535&i,this._a16=65535&a,this._a32=65535&s,this._a48=65535&h,this},i.prototype.subtract=function(t){return this.add(t.clone().negate())},i.prototype.multiply=function(t){var i=this._a00,a=this._a16,s=this._a32,h=this._a48,_=t._a00,r=t._a16,e=t._a32,n=t._a48,o=i*_,u=o>>>16;u+=i*r;var p=u>>>16;u&=65535,u+=a*_,p+=u>>>16,p+=i*e;var f=p>>>16;return p&=65535,p+=a*r,f+=p>>>16,p&=65535,p+=s*_,f+=p>>>16,f+=i*n,f&=65535,f+=a*e,f&=65535,f+=s*r,f&=65535,f+=h*_,this._a00=65535&o,this._a16=65535&u,this._a32=65535&p,this._a48=65535&f,this},i.prototype.div=function(t){if(0==t._a16&&0==t._a32&&0==t._a48){if(0==t._a00)throw Error("division by zero");if(1==t._a00)return this.remainder=new i(0),this}if(t.gt(this))return this.remainder=this.clone(),this._a00=0,this._a16=0,this._a32=0,this._a48=0,this;if(this.eq(t))return this.remainder=new i(0),this._a00=1,this._a16=0,this._a32=0,this._a48=0,this;for(var a=t.clone(),s=-1;!this.lt(a);)a.shiftLeft(1,!0),s++;for(this.remainder=this.clone(),this._a00=0,this._a16=0,this._a32=0,this._a48=0;s>=0;s--)a.shiftRight(1),this.remainder.lt(a)||(this.remainder.subtract(a),s>=48?this._a48|=1<<s-48:s>=32?this._a32|=1<<s-32:s>=16?this._a16|=1<<s-16:this._a00|=1<<s);return this},i.prototype.negate=function(){var t=(65535&~this._a00)+1;return this._a00=65535&t,t=(65535&~this._a16)+(t>>>16),this._a16=65535&t,t=(65535&~this._a32)+(t>>>16),this._a32=65535&t,this._a48=~this._a48+(t>>>16)&65535,this},i.prototype.equals=i.prototype.eq=function(t){return this._a48==t._a48&&this._a00==t._a00&&this._a32==t._a32&&this._a16==t._a16},i.prototype.greaterThan=i.prototype.gt=function(t){return this._a48>t._a48?!0:this._a48<t._a48?!1:this._a32>t._a32?!0:this._a32<t._a32?!1:this._a16>t._a16?!0:this._a16<t._a16?!1:this._a00>t._a00},i.prototype.lessThan=i.prototype.lt=function(t){return this._a48<t._a48?!0:this._a48>t._a48?!1:this._a32<t._a32?!0:this._a32>t._a32?!1:this._a16<t._a16?!0:this._a16>t._a16?!1:this._a00<t._a00},i.prototype.or=function(t){return this._a00|=t._a00,this._a16|=t._a16,this._a32|=t._a32,this._a48|=t._a48,this},i.prototype.and=function(t){return this._a00&=t._a00,this._a16&=t._a16,this._a32&=t._a32,this._a48&=t._a48,this},i.prototype.xor=function(t){return this._a00^=t._a00,this._a16^=t._a16,this._a32^=t._a32,this._a48^=t._a48,this},i.prototype.not=function(){return this._a00=65535&~this._a00,this._a16=65535&~this._a16,this._a32=65535&~this._a32,this._a48=65535&~this._a48,this},i.prototype.shiftRight=i.prototype.shiftr=function(t){return t%=64,t>=48?(this._a00=this._a48>>t-48,this._a16=0,this._a32=0,this._a48=0):t>=32?(t-=32,this._a00=65535&(this._a32>>t|this._a48<<16-t),this._a16=this._a48>>t&65535,this._a32=0,this._a48=0):t>=16?(t-=16,this._a00=65535&(this._a16>>t|this._a32<<16-t),this._a16=65535&(this._a32>>t|this._a48<<16-t),this._a32=this._a48>>t&65535,this._a48=0):(this._a00=65535&(this._a00>>t|this._a16<<16-t),this._a16=65535&(this._a16>>t|this._a32<<16-t),this._a32=65535&(this._a32>>t|this._a48<<16-t),this._a48=this._a48>>t&65535),this},i.prototype.shiftLeft=i.prototype.shiftl=function(t,i){return t%=64,t>=48?(this._a48=this._a00<<t-48,this._a32=0,this._a16=0,this._a00=0):t>=32?(t-=32,this._a48=this._a16<<t|this._a00>>16-t,this._a32=this._a00<<t&65535,this._a16=0,this._a00=0):t>=16?(t-=16,this._a48=this._a32<<t|this._a16>>16-t,this._a32=65535&(this._a16<<t|this._a00>>16-t),this._a16=this._a00<<t&65535,this._a00=0):(this._a48=this._a48<<t|this._a32>>16-t,this._a32=65535&(this._a32<<t|this._a16>>16-t),this._a16=65535&(this._a16<<t|this._a00>>16-t),this._a00=this._a00<<t&65535),i||(this._a48&=65535),this},i.prototype.rotateLeft=i.prototype.rotl=function(t){if(t%=64,0==t)return this;if(t>=32){var i=this._a00;if(this._a00=this._a32,this._a32=i,i=this._a48,this._a48=this._a16,this._a16=i,32==t)return this;t-=32}var a=this._a48<<16|this._a32,s=this._a16<<16|this._a00,h=a<<t|s>>>32-t,_=s<<t|a>>>32-t;return this._a00=65535&_,this._a16=_>>>16,this._a32=65535&h,this._a48=h>>>16,this},i.prototype.rotateRight=i.prototype.rotr=function(t){if(t%=64,0==t)return this;if(t>=32){var i=this._a00;if(this._a00=this._a32,this._a32=i,i=this._a48,this._a48=this._a16,this._a16=i,32==t)return this;t-=32}var a=this._a48<<16|this._a32,s=this._a16<<16|this._a00,h=a>>>t|s<<32-t,_=s>>>t|a<<32-t;return this._a00=65535&_,this._a16=_>>>16,this._a32=65535&h,this._a48=h>>>16,this},i.prototype.clone=function(){return new i(this._a00,this._a16,this._a32,this._a48)},"undefined"!=typeof define&&define.amd?define([],function(){return i}):"undefined"!=typeof module&&module.exports?module.exports=i:t.UINT64=i}(this);