const express = require('express');
const router = express.Router();
const { RtcTokenBuilder, RtcRole } = require('agora-access-token');

router.get('/oauth/callback', async (req, res) => {
  const code = req.query.code;

  if (!code) {
    return res.status(400).send('Authorization code missing');
  }

  try {
    const tokenData = await getAccessTokenFromCode(code);
    // Save tokenData.access_token and tokenData.refresh_token securely for this user

    res.json({ message: 'Authorization successful', tokenData });
  } catch (error) {
    console.error('OAuth token error:', error.response?.data || error.message);
    res.status(500).send('Failed to get access token');
  }
});

// Example endpoint to create meeting using saved token (token management required)
router.post('/create', async (req, res) => {
  // In a real app, you must get the user’s saved access_token from DB or session
  const accessToken = req.body.accessToken; // for testing, pass in request body

  if (!accessToken) {
    return res.status(400).json({ error: 'Access token required' });
  }

  try {
    const meeting = await createMeeting(accessToken);
    res.json({ meetingUrl: meeting.join_url, meetingId: meeting.id });
  } catch (error) {
    console.error('Create meeting error:', error.response?.data || error.message);
    res.status(500).json({ error: 'Failed to create meeting' });
  }
});

// Generate Agora token for video call
router.post('/agora-token', async (req, res) => {
  try {
    const { channelName, userId } = req.body;

    if (!channelName || !userId) {
      return res.status(400).json({
        success: false,
        error: 'Channel name and user ID are required'
      });
    }

    // Agora credentials (replace with your actual credentials)
    const appId = '51d04cab7dc84df589c15e084a154c0a';
    const appCertificate = 'your_app_certificate_here'; // Replace with actual certificate
    const role = RtcRole.PUBLISHER;
    const expirationTimeInSeconds = 3600; // 1 hour
    const currentTimestamp = Math.floor(Date.now() / 1000);
    const privilegeExpiredTs = currentTimestamp + expirationTimeInSeconds;

    // Generate token
    const token = RtcTokenBuilder.buildTokenWithUid(
      appId,
      appCertificate,
      channelName,
      parseInt(userId) || 0,
      role,
      privilegeExpiredTs
    );

    console.log('🎥 Generated Agora token for channel:', channelName);

    res.json({
      success: true,
      data: {
        token: token,
        appId: appId,
        channelName: channelName,
        userId: userId,
        expiresAt: privilegeExpiredTs
      }
    });
  } catch (error) {
    console.error('❌ Error generating Agora token:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate video call token'
    });
  }
});

// Create meeting endpoint
router.post('/create-meeting', async (req, res) => {
  try {
    const { topic, start_time, duration, host_id } = req.body;

    if (!topic || !host_id) {
      return res.status(400).json({
        success: false,
        error: 'Topic and host ID are required'
      });
    }

    // Generate a unique meeting ID
    const meetingId = `meeting_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const channelName = `channel_${meetingId}`;

    // For demo purposes, create a simple meeting object
    const meeting = {
      id: meetingId,
      topic: topic,
      start_time: start_time || new Date().toISOString(),
      duration: duration || 60,
      host_id: host_id,
      join_url: `${req.protocol}://${req.get('host')}/video-call?channel=${channelName}&meeting=${meetingId}`,
      channel_name: channelName,
      status: 'scheduled'
    };

    console.log('📅 Created meeting:', meeting.id);

    res.json({
      success: true,
      data: meeting
    });
  } catch (error) {
    console.error('❌ Error creating meeting:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create meeting'
    });
  }
});

// Join meeting endpoint
router.get('/join/:meetingId', async (req, res) => {
  try {
    const { meetingId } = req.params;

    if (!meetingId) {
      return res.status(400).json({
        success: false,
        error: 'Meeting ID is required'
      });
    }

    // For demo purposes, return meeting info
    const meeting = {
      id: meetingId,
      channel_name: `channel_${meetingId}`,
      status: 'active',
      join_url: `${req.protocol}://${req.get('host')}/video-call?channel=channel_${meetingId}&meeting=${meetingId}`
    };

    console.log('🎥 Joining meeting:', meetingId);

    res.json({
      success: true,
      data: meeting
    });
  } catch (error) {
    console.error('❌ Error joining meeting:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to join meeting'
    });
  }
});

module.exports = router;
