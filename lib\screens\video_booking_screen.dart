import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;

class VideoBookingScreen extends StatefulWidget {
  final String userId;

  const VideoBookingScreen({super.key, required this.userId});

  @override
  _VideoBookingScreenState createState() => _VideoBookingScreenState();
}

class _VideoBookingScreenState extends State<VideoBookingScreen> {
  List<Map<String, dynamic>> availableTherapists = [];
  List<Map<String, dynamic>> upcomingAppointments = [];
  bool isLoading = false;
  String selectedSessionType = 'individual';

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      isLoading = true;
    });

    await Future.wait([
      _fetchAvailableTherapists(),
      _fetchUpcomingAppointments(),
    ]);

    setState(() {
      isLoading = false;
    });
  }

  Future<void> _fetchAvailableTherapists() async {
    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      
      setState(() {
        availableTherapists = [
          {
            'id': '1',
            'name': 'Dr. <PERSON>',
            'specialization': 'Anxiety & Depression',
            'rating': 4.8,
            'experience': '8 years',
            'price': 120,
            'nextAvailable': DateTime.now().add(const Duration(hours: 2)),
            'image': 'https://via.placeholder.com/150',
            'languages': ['English', 'Spanish'],
            'timeSlots': [
              '09:00 AM', '10:30 AM', '02:00 PM', '03:30 PM', '05:00 PM'
            ],
          },
          {
            'id': '2',
            'name': 'Dr. Michael Chen',
            'specialization': 'Cognitive Behavioral Therapy',
            'rating': 4.9,
            'experience': '12 years',
            'price': 150,
            'nextAvailable': DateTime.now().add(const Duration(hours: 4)),
            'image': 'https://via.placeholder.com/150',
            'languages': ['English', 'Mandarin'],
            'timeSlots': [
              '08:00 AM', '11:00 AM', '01:00 PM', '04:00 PM'
            ],
          },
          {
            'id': '3',
            'name': 'Dr. Emily Rodriguez',
            'specialization': 'Family Therapy',
            'rating': 4.7,
            'experience': '10 years',
            'price': 130,
            'nextAvailable': DateTime.now().add(const Duration(days: 1)),
            'image': 'https://via.placeholder.com/150',
            'languages': ['English', 'Spanish', 'Portuguese'],
            'timeSlots': [
              '09:30 AM', '12:00 PM', '02:30 PM', '04:30 PM'
            ],
          },
        ];
      });
    } catch (e) {
      print('Error fetching therapists: $e');
    }
  }

  Future<void> _fetchUpcomingAppointments() async {
    try {
      final response = await http.get(
        Uri.parse('http://localhost:3000/api/appointments/${widget.userId}'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        setState(() {
          upcomingAppointments = List<Map<String, dynamic>>.from(data['appointments'] ?? []);
        });
      }
    } catch (e) {
      print('Error fetching appointments: $e');
    }
  }

  Future<void> _bookAppointment(Map<String, dynamic> therapist, String timeSlot) async {
    try {
      final response = await http.post(
        Uri.parse('http://localhost:3000/api/appointments'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'userId': widget.userId,
          'therapistId': therapist['id'],
          'therapistName': therapist['name'],
          'sessionType': selectedSessionType,
          'appointmentType': 'video',
          'timeSlot': timeSlot,
          'price': therapist['price'],
          'date': DateTime.now().add(const Duration(days: 1)).toIso8601String(),
          'status': 'scheduled',
        }),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Appointment booked with ${therapist['name']}!'),
            backgroundColor: Colors.green,
          ),
        );
        _fetchUpcomingAppointments();
      } else {
        throw Exception('Failed to book appointment');
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error booking appointment: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Video Consultations'),
        backgroundColor: Colors.teal,
      ),
      body: DefaultTabController(
        length: 2,
        child: Column(
          children: [
            const TabBar(
              labelColor: Colors.teal,
              tabs: [
                Tab(text: 'Book Session', icon: Icon(Icons.video_call)),
                Tab(text: 'My Appointments', icon: Icon(Icons.schedule)),
              ],
            ),
            Expanded(
              child: TabBarView(
                children: [
                  _buildBookingTab(),
                  _buildAppointmentsTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBookingTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSessionTypeSelector(),
          const SizedBox(height: 20),
          Text(
            'Available Therapists',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          if (isLoading)
            const Center(child: CircularProgressIndicator())
          else
            ...availableTherapists.map((therapist) => _buildTherapistCard(therapist)).toList(),
        ],
      ),
    );
  }

  Widget _buildSessionTypeSelector() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Session Type',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: RadioListTile<String>(
                    title: const Text('Individual'),
                    subtitle: const Text('One-on-one session'),
                    value: 'individual',
                    groupValue: selectedSessionType,
                    onChanged: (value) {
                      setState(() {
                        selectedSessionType = value!;
                      });
                    },
                    activeColor: Colors.teal,
                  ),
                ),
                Expanded(
                  child: RadioListTile<String>(
                    title: const Text('Couple'),
                    subtitle: const Text('Relationship therapy'),
                    value: 'couple',
                    groupValue: selectedSessionType,
                    onChanged: (value) {
                      setState(() {
                        selectedSessionType = value!;
                      });
                    },
                    activeColor: Colors.teal,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTherapistCard(Map<String, dynamic> therapist) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: Colors.teal.withOpacity(0.2),
                  child: Text(
                    therapist['name'].split(' ').map((n) => n[0]).join(),
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.teal,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        therapist['name'],
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        therapist['specialization'],
                        style: TextStyle(color: Colors.grey[600]),
                      ),
                      Row(
                        children: [
                          Icon(Icons.star, color: Colors.amber, size: 16),
                          Text(' ${therapist['rating']} • '),
                          Text('${therapist['experience']} experience'),
                        ],
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '\$${therapist['price']}',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    const Text('per session'),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              'Languages: ${therapist['languages'].join(', ')}',
              style: TextStyle(color: Colors.grey[600]),
            ),
            const SizedBox(height: 12),
            Text(
              'Available Time Slots:',
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: therapist['timeSlots'].map<Widget>((slot) =>
                OutlinedButton(
                  onPressed: () => _showBookingConfirmation(therapist, slot),
                  style: OutlinedButton.styleFrom(
                    side: const BorderSide(color: Colors.teal),
                  ),
                  child: Text(slot),
                ),
              ).toList(),
            ),
          ],
        ),
      ),
    );
  }

  void _showBookingConfirmation(Map<String, dynamic> therapist, String timeSlot) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Booking'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Therapist: ${therapist['name']}'),
            Text('Session Type: ${selectedSessionType.toUpperCase()}'),
            Text('Time: $timeSlot'),
            Text('Price: \$${therapist['price']}'),
            const SizedBox(height: 16),
            const Text(
              'You will receive a video call link via email before the session.',
              style: TextStyle(fontStyle: FontStyle.italic),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _bookAppointment(therapist, timeSlot);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.teal),
            child: const Text('Confirm Booking'),
          ),
        ],
      ),
    );
  }

  Widget _buildAppointmentsTab() {
    if (upcomingAppointments.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.event_busy, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No upcoming appointments',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            SizedBox(height: 8),
            Text(
              'Book your first session to get started',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: upcomingAppointments.length,
      itemBuilder: (context, index) {
        final appointment = upcomingAppointments[index];
        return _buildAppointmentCard(appointment);
      },
    );
  }

  Widget _buildAppointmentCard(Map<String, dynamic> appointment) {
    final date = DateTime.parse(appointment['date']);
    final isToday = DateTime.now().day == date.day;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: isToday ? Colors.green : Colors.teal,
          child: Icon(
            isToday ? Icons.today : Icons.video_call,
            color: Colors.white,
          ),
        ),
        title: Text(appointment['therapistName']),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${appointment['sessionType']} session'),
            Text('${date.day}/${date.month}/${date.year} at ${appointment['timeSlot']}'),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (isToday)
              ElevatedButton(
                onPressed: () {
                  // Join video call
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Joining video call...'),
                      backgroundColor: Colors.green,
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  minimumSize: const Size(80, 30),
                ),
                child: const Text('Join'),
              )
            else
              Text(
                appointment['status'].toUpperCase(),
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.orange,
                ),
              ),
          ],
        ),
        isThreeLine: true,
      ),
    );
  }
}
