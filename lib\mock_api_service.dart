import 'dart:convert';

class MockApiService {
  // Mock therapist data
  static final List<Map<String, dynamic>> _mockTherapists = [
    {
      'id': 'therapist-1',
      'name': 'Dr. Test Therapist',
      'email': '<EMAIL>',
      'password': 'password123',
      'specialization': 'General Therapy',
      'rating': 4.8,
      'experience': '5 years',
      'location': 'New York, NY',
      'avatar': 'https://via.placeholder.com/150',
    },
    {
      'id': 'therapist-2',
      'name': 'Dr. <PERSON>',
      'email': '<EMAIL>',
      'password': 'password123',
      'specialization': 'Anxiety & Depression',
      'rating': 4.9,
      'experience': '8 years',
      'location': 'Los Angeles, CA',
      'avatar': 'https://via.placeholder.com/150',
    },
    {
      'id': 'therapist-3',
      'name': 'Dr. <PERSON>',
      'email': '<EMAIL>',
      'password': 'password123',
      'specialization': 'Cognitive Behavioral Therapy',
      'rating': 4.7,
      'experience': '6 years',
      'location': 'Chicago, IL',
      'avatar': 'https://via.placeholder.com/150',
    },
  ];

  // Mock user data
  static final List<Map<String, dynamic>> _mockUsers = [
    {
      'id': 'user-1',
      'name': 'Test User',
      'email': '<EMAIL>',
      'password': 'password123',
      'role': 'user',
    },
  ];

  static Future<Map<String, dynamic>> login(
      String email, String password, String userType) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 1000));

    try {
      if (userType == 'therapist') {
        // Check therapist credentials
        final therapist = _mockTherapists.firstWhere(
          (t) => t['email'] == email && t['password'] == password,
          orElse: () => {},
        );

        if (therapist.isNotEmpty) {
          return {
            'success': true,
            'data': {
              'user': {
                'id': therapist['id'],
                'name': therapist['name'],
                'email': therapist['email'],
                'role': 'therapist',
                'specialization': therapist['specialization'],
              },
              'token': 'mock-jwt-token-${therapist['id']}',
            },
            'message': 'Therapist login successful',
          };
        } else {
          return {
            'success': false,
            'error': 'Invalid therapist credentials',
          };
        }
      } else {
        // Check user credentials
        final user = _mockUsers.firstWhere(
          (u) => u['email'] == email && u['password'] == password,
          orElse: () => {},
        );

        if (user.isNotEmpty) {
          return {
            'success': true,
            'data': {
              'user': {
                'id': user['id'],
                'name': user['name'],
                'email': user['email'],
                'role': 'user',
              },
              'token': 'mock-jwt-token-${user['id']}',
            },
            'message': 'User login successful',
          };
        } else {
          return {
            'success': false,
            'error': 'Invalid user credentials',
          };
        }
      }
    } catch (e) {
      return {
        'success': false,
        'error': 'Login failed: $e',
      };
    }
  }

  static Future<Map<String, dynamic>> signup(
      String username, String email, String password, String role) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 1000));

    try {
      if (role == 'therapist') {
        // Check if therapist already exists
        final existingTherapist = _mockTherapists.any((t) => t['email'] == email);
        if (existingTherapist) {
          return {
            'success': false,
            'error': 'Therapist with this email already exists',
          };
        }

        // Create new therapist
        final newTherapist = {
          'id': 'therapist-${_mockTherapists.length + 1}',
          'name': username,
          'email': email,
          'password': password,
          'specialization': 'General Therapy',
          'rating': 4.5,
          'experience': '1 year',
          'location': 'Remote',
          'avatar': 'https://via.placeholder.com/150',
        };

        _mockTherapists.add(newTherapist);

        return {
          'success': true,
          'data': {
            'user': {
              'id': newTherapist['id'],
              'name': newTherapist['name'],
              'email': newTherapist['email'],
              'role': 'therapist',
              'specialization': newTherapist['specialization'],
            },
            'token': 'mock-jwt-token-${newTherapist['id']}',
          },
          'message': 'Therapist account created successfully',
        };
      } else {
        // Check if user already exists
        final existingUser = _mockUsers.any((u) => u['email'] == email);
        if (existingUser) {
          return {
            'success': false,
            'error': 'User with this email already exists',
          };
        }

        // Create new user
        final newUser = {
          'id': 'user-${_mockUsers.length + 1}',
          'name': username,
          'email': email,
          'password': password,
          'role': 'user',
        };

        _mockUsers.add(newUser);

        return {
          'success': true,
          'data': {
            'user': {
              'id': newUser['id'],
              'name': newUser['name'],
              'email': newUser['email'],
              'role': 'user',
            },
            'token': 'mock-jwt-token-${newUser['id']}',
          },
          'message': 'User account created successfully',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'error': 'Signup failed: $e',
      };
    }
  }

  static Future<Map<String, dynamic>> getAllTherapists() async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));

    try {
      return {
        'success': true,
        'data': _mockTherapists.map((t) => {
          'id': t['id'],
          'name': t['name'],
          'email': t['email'],
          'specialization': t['specialization'],
          'rating': t['rating'],
          'experience': t['experience'],
          'location': t['location'],
          'avatar': t['avatar'],
        }).toList(),
        'message': 'Therapists retrieved successfully',
      };
    } catch (e) {
      return {
        'success': false,
        'error': 'Failed to get therapists: $e',
      };
    }
  }
}
