<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MindEase Database Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #8B5CF6 0%, #A855F7 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .dashboard {
            padding: 30px;
        }
        
        .status-card {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .status-connected {
            border-left: 5px solid #10b981;
        }
        
        .status-disconnected {
            border-left: 5px solid #ef4444;
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
        }
        
        .card h3 {
            color: #1f2937;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }
        
        .btn {
            background: #8B5CF6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #7C3AED;
        }
        
        .btn-danger {
            background: #ef4444;
        }
        
        .btn-danger:hover {
            background: #dc2626;
        }
        
        .data-container {
            max-height: 300px;
            overflow-y: auto;
            background: #f8fafc;
            border-radius: 5px;
            padding: 10px;
            margin-top: 10px;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #6b7280;
        }
        
        .error {
            color: #ef4444;
            background: #fef2f2;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .success {
            color: #10b981;
            background: #f0fdf4;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        pre {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 MindEase Database Dashboard</h1>
            <p>MongoDB Connection & Data Management</p>
        </div>
        
        <div class="dashboard">
            <!-- Connection Status -->
            <div id="connectionStatus" class="status-card">
                <h3>📡 Database Connection Status</h3>
                <div id="connectionInfo" class="loading">Checking connection...</div>
            </div>
            
            <!-- Actions -->
            <div class="card">
                <h3>🔧 Database Actions</h3>
                <button class="btn" onclick="checkConnection()">🔄 Refresh Connection</button>
                <button class="btn" onclick="seedDatabase()">🌱 Seed Sample Data</button>
                <button class="btn btn-danger" onclick="clearDatabase()">🗑️ Clear All Data</button>
            </div>
            
            <!-- Data Grid -->
            <div class="grid">
                <!-- Users -->
                <div class="card">
                    <h3>👥 Users</h3>
                    <button class="btn" onclick="loadUsers()">Load Users</button>
                    <div id="usersData" class="data-container"></div>
                </div>
                
                <!-- Moods -->
                <div class="card">
                    <h3>😊 Mood Entries</h3>
                    <button class="btn" onclick="loadMoods()">Load Moods</button>
                    <div id="moodsData" class="data-container"></div>
                </div>
                
                <!-- Journals -->
                <div class="card">
                    <h3>📝 Journal Entries</h3>
                    <button class="btn" onclick="loadJournals()">Load Journals</button>
                    <div id="journalsData" class="data-container"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = '/api/db-test';
        
        // Check database connection
        async function checkConnection() {
            try {
                const response = await fetch(`${API_BASE}/connection`);
                const data = await response.json();
                
                const statusDiv = document.getElementById('connectionStatus');
                const infoDiv = document.getElementById('connectionInfo');
                
                if (data.success) {
                    statusDiv.className = 'status-card status-connected';
                    infoDiv.innerHTML = `
                        <div class="success">✅ Connected to MongoDB</div>
                        <pre>${JSON.stringify(data.connection, null, 2)}</pre>
                    `;
                } else {
                    statusDiv.className = 'status-card status-disconnected';
                    infoDiv.innerHTML = `<div class="error">❌ Connection failed: ${data.message}</div>`;
                }
            } catch (error) {
                const statusDiv = document.getElementById('connectionStatus');
                const infoDiv = document.getElementById('connectionInfo');
                statusDiv.className = 'status-card status-disconnected';
                infoDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }
        
        // Seed database with sample data
        async function seedDatabase() {
            if (!confirm('This will clear existing data and create sample data. Continue?')) return;
            
            try {
                const response = await fetch(`${API_BASE}/seed`, { method: 'POST' });
                const data = await response.json();
                
                if (data.success) {
                    alert(`✅ Sample data created successfully!\n\nCreated:\n- ${data.data.users} users\n- ${data.data.moods} mood entries\n- ${data.data.journals} journal entries`);
                    checkConnection();
                } else {
                    alert(`❌ Failed to create sample data: ${data.message}`);
                }
            } catch (error) {
                alert(`❌ Error: ${error.message}`);
            }
        }
        
        // Clear all database data
        async function clearDatabase() {
            if (!confirm('⚠️ This will permanently delete ALL data from the database. Are you sure?')) return;
            
            try {
                const response = await fetch(`${API_BASE}/clear`, { method: 'DELETE' });
                const data = await response.json();
                
                if (data.success) {
                    alert(`✅ All data cleared successfully!\n\nDeleted:\n- ${data.deletedCounts.users} users\n- ${data.deletedCounts.moods} moods\n- ${data.deletedCounts.journals} journals\n- ${data.deletedCounts.payments} payments\n- ${data.deletedCounts.appointments} appointments\n- ${data.deletedCounts.therapists} therapists`);
                    checkConnection();
                    // Clear all data displays
                    document.getElementById('usersData').innerHTML = '';
                    document.getElementById('moodsData').innerHTML = '';
                    document.getElementById('journalsData').innerHTML = '';
                } else {
                    alert(`❌ Failed to clear data: ${data.message}`);
                }
            } catch (error) {
                alert(`❌ Error: ${error.message}`);
            }
        }
        
        // Load users data
        async function loadUsers() {
            const container = document.getElementById('usersData');
            container.innerHTML = '<div class="loading">Loading users...</div>';
            
            try {
                const response = await fetch(`${API_BASE}/users`);
                const data = await response.json();
                
                if (data.success) {
                    container.innerHTML = `
                        <div class="success">Found ${data.count} users</div>
                        <pre>${JSON.stringify(data.users, null, 2)}</pre>
                    `;
                } else {
                    container.innerHTML = `<div class="error">Failed to load users: ${data.message}</div>`;
                }
            } catch (error) {
                container.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }
        
        // Load moods data
        async function loadMoods() {
            const container = document.getElementById('moodsData');
            container.innerHTML = '<div class="loading">Loading moods...</div>';
            
            try {
                const response = await fetch(`${API_BASE}/moods`);
                const data = await response.json();
                
                if (data.success) {
                    container.innerHTML = `
                        <div class="success">Found ${data.count} mood entries</div>
                        <pre>${JSON.stringify(data.moods, null, 2)}</pre>
                    `;
                } else {
                    container.innerHTML = `<div class="error">Failed to load moods: ${data.message}</div>`;
                }
            } catch (error) {
                container.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }
        
        // Load journals data
        async function loadJournals() {
            const container = document.getElementById('journalsData');
            container.innerHTML = '<div class="loading">Loading journals...</div>';
            
            try {
                const response = await fetch(`${API_BASE}/journals`);
                const data = await response.json();
                
                if (data.success) {
                    container.innerHTML = `
                        <div class="success">Found ${data.count} journal entries</div>
                        <pre>${JSON.stringify(data.journals, null, 2)}</pre>
                    `;
                } else {
                    container.innerHTML = `<div class="error">Failed to load journals: ${data.message}</div>`;
                }
            } catch (error) {
                container.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }
        
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            checkConnection();
        });
    </script>
</body>
</html>
