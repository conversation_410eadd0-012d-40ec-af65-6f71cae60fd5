import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:myapp/login_page.dart';
import 'package:myapp/api_service.dart';
import 'package:myapp/services/therapist_logger.dart';
import 'package:myapp/screens/therapist_logs_screen.dart';
import 'package:shared_preferences/shared_preferences.dart';

class TherapistDashboard extends StatefulWidget {
  final String therapistId;

  const TherapistDashboard({Key? key, required this.therapistId})
      : super(key: key);

  @override
  State<TherapistDashboard> createState() => _TherapistDashboardState();
}

class _TherapistDashboardState extends State<TherapistDashboard> {
  int _selectedIndex = 0;
  DateTime _currentDate = DateTime.now();
  final DateFormat _dateFormat = DateFormat('yyyy-MM-dd');

  // Real data from API
  Map<String, dynamic>? _therapistData;
  List<Map<String, dynamic>> _appointments = [];
  List<Map<String, dynamic>> _patients = [];
  List<Map<String, dynamic>> _todayAppointments = [];
  Map<String, dynamic> _statistics = {};
  bool _isLoading = true;
  String _error = '';

  // Logger instance
  final TherapistLogger _logger = TherapistLogger();

  @override
  void initState() {
    super.initState();

    // Initialize logger
    _logger.initialize(widget.therapistId);

    // Log dashboard access
    _logger.logInfo(
      category: LogCategory.userInteraction,
      message: 'Therapist dashboard accessed',
      metadata: {
        'therapistId': widget.therapistId,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );

    _loadTherapistData();
  }

  Future<void> _loadTherapistData() async {
    setState(() {
      _isLoading = true;
      _error = '';
    });

    try {
      // Load therapist profile
      await _loadTherapistProfile();

      // Load appointments from database
      await _loadAppointments();

      // Load patients from database
      await _loadPatients();

      // Calculate statistics
      _calculateStatistics();
    } catch (e) {
      setState(() {
        _error = 'Failed to load data: $e';
      });

      _logger.logError(
        category: LogCategory.dataAccess,
        message: 'Failed to load therapist dashboard data',
        metadata: {
          'therapistId': widget.therapistId,
          'error': e.toString(),
        },
        stackTrace: e.toString(),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Map<String, dynamic> _getTherapistData(String therapistId) {
    // Comprehensive therapist database
    final therapists = {
      'therapist-1': {
        'id': 'therapist-1',
        'name': 'Dr. Sarah Johnson',
        'email': '<EMAIL>',
        'specialization': 'Anxiety & Depression Specialist',
        'experience': '8 years',
        'rating': 4.9,
        'location': 'New York, NY',
        'phone': '+****************',
        'education': 'PhD in Clinical Psychology, Harvard University',
        'license': 'NY-PSY-12345',
        'bio':
            'Specialized in cognitive behavioral therapy with focus on anxiety and depression treatment.',
        'totalPatients': 127,
        'completedSessions': 1,
        'upcomingAppointments': 8,
        'pendingTasks': 5,
      },
      'therapist-2': {
        'id': 'therapist-2',
        'name': 'Dr. Michael Chen',
        'email': '<EMAIL>',
        'specialization': 'Cognitive Behavioral Therapy',
        'experience': '6 years',
        'rating': 4.7,
        'location': 'Los Angeles, CA',
        'phone': '+****************',
        'education': 'PhD in Psychology, UCLA',
        'license': 'CA-PSY-67890',
        'bio': 'Expert in CBT techniques for trauma and PTSD recovery.',
        'totalPatients': 89,
        'completedSessions': 892,
        'upcomingAppointments': 12,
        'pendingTasks': 3,
      },
      'test-therapist-id': {
        'id': 'test-therapist-id',
        'name': 'Dr. Emily Rodriguez',
        'email': '<EMAIL>',
        'specialization': 'Family & Relationship Therapy',
        'experience': '10 years',
        'rating': 4.8,
        'location': 'Chicago, IL',
        'phone': '+****************',
        'education':
            'PhD in Marriage & Family Therapy, Northwestern University',
        'license': 'IL-MFT-54321',
        'bio':
            'Dedicated to helping families and couples build stronger relationships.',
        'totalPatients': 156,
        'completedSessions': 1247,
        'upcomingAppointments': 6,
        'pendingTasks': 7,
      },
    };

    return therapists[therapistId] ?? therapists['therapist-1']!;
  }

  Future<void> _loadTherapistProfile() async {
    try {
      // Try to get therapist data from API
      final result = await ApiService.getUserById(widget.therapistId);

      if (result['success'] && result['data'] != null) {
        _therapistData = result['data'];
      } else {
        // If API fails, show error but don't crash
        throw Exception('Failed to load therapist profile from API');
      }
    } catch (e) {
      _logger.logError(
        category: LogCategory.apiCall,
        message: 'Failed to load therapist profile',
        metadata: {
          'therapistId': widget.therapistId,
          'error': e.toString(),
        },
        stackTrace: e.toString(),
      );

      // For now, we'll show an error instead of mock data
      throw Exception(
          'Unable to connect to server. Please check your internet connection.');
    }
  }

  Future<void> _loadAppointments() async {
    try {
      // Get appointments for this therapist
      final result = await ApiService.getUserAppointments(widget.therapistId);

      if (result['success'] && result['data'] != null) {
        final List<dynamic> appointmentData = result['data'];

        _appointments = appointmentData
            .map((apt) => {
                  'id': apt['_id'] ?? apt['id'],
                  'patientName': apt['patientName'] ??
                      apt['patient_name'] ??
                      'Unknown Patient',
                  'patientId': apt['patientId'] ?? apt['patient_id'],
                  'time': apt['time'] ?? '00:00',
                  'date': apt['date'] ?? DateTime.now().toIso8601String(),
                  'duration': apt['duration'] ?? 60,
                  'type': apt['type'] ?? 'Consultation',
                  'status': apt['status'] ?? 'scheduled',
                  'notes': apt['notes'] ?? '',
                })
            .toList()
            .cast<Map<String, dynamic>>();

        // Filter today's appointments
        final today = DateTime.now();
        _todayAppointments = _appointments.where((apt) {
          try {
            final aptDate = DateTime.parse(apt['date']);
            return aptDate.year == today.year &&
                aptDate.month == today.month &&
                aptDate.day == today.day;
          } catch (e) {
            return false;
          }
        }).toList();
      } else {
        _appointments = [];
        _todayAppointments = [];
      }
    } catch (e) {
      _logger.logError(
        category: LogCategory.apiCall,
        message: 'Failed to load appointments',
        metadata: {
          'therapistId': widget.therapistId,
          'error': e.toString(),
        },
        stackTrace: e.toString(),
      );

      _appointments = [];
      _todayAppointments = [];
    }
  }

  Future<void> _loadPatients() async {
    try {
      // Get patients for this therapist
      // This would typically be a separate API call to get therapist's patients
      final result = await ApiService.getUserById(widget.therapistId);

      if (result['success'] && result['data'] != null) {
        // For now, we'll extract unique patients from appointments
        final Set<String> uniquePatientIds = {};
        final List<Map<String, dynamic>> patients = [];

        for (final apt in _appointments) {
          final patientId = apt['patientId'];
          if (patientId != null && !uniquePatientIds.contains(patientId)) {
            uniquePatientIds.add(patientId);

            // Try to get patient details
            try {
              final patientResult = await ApiService.getUserById(patientId);
              if (patientResult['success'] && patientResult['data'] != null) {
                final patientData = patientResult['data'];
                patients.add({
                  'id': patientData['_id'] ?? patientData['id'],
                  'name': patientData['name'] ??
                      apt['patientName'] ??
                      'Unknown Patient',
                  'email': patientData['email'] ?? '',
                  'age': patientData['age'] ?? 0,
                  'phone': patientData['phone'] ?? '',
                  'nextAppointment': _getNextAppointmentForPatient(patientId),
                  'lastSession': _getLastSessionForPatient(patientId),
                  'totalSessions': _getTotalSessionsForPatient(patientId),
                });
              }
            } catch (e) {
              // If we can't get patient details, create basic info from appointment
              patients.add({
                'id': patientId,
                'name': apt['patientName'] ?? 'Unknown Patient',
                'email': '',
                'age': 0,
                'phone': '',
                'nextAppointment': apt['date'],
                'lastSession': '',
                'totalSessions': 1,
              });
            }
          }
        }

        _patients = patients;
      } else {
        _patients = [];
      }
    } catch (e) {
      _logger.logError(
        category: LogCategory.dataAccess,
        message: 'Failed to load patients',
        metadata: {
          'therapistId': widget.therapistId,
          'error': e.toString(),
        },
        stackTrace: e.toString(),
      );

      _patients = [];
    }
  }

  String? _getNextAppointmentForPatient(String patientId) {
    final now = DateTime.now();
    final futureAppointments = _appointments
        .where((apt) => apt['patientId'] == patientId)
        .where((apt) {
      try {
        final aptDate = DateTime.parse(apt['date']);
        return aptDate.isAfter(now);
      } catch (e) {
        return false;
      }
    }).toList();

    if (futureAppointments.isNotEmpty) {
      futureAppointments.sort((a, b) =>
          DateTime.parse(a['date']).compareTo(DateTime.parse(b['date'])));
      return futureAppointments.first['date'];
    }
    return null;
  }

  String? _getLastSessionForPatient(String patientId) {
    final now = DateTime.now();
    final pastAppointments = _appointments
        .where((apt) => apt['patientId'] == patientId)
        .where((apt) {
      try {
        final aptDate = DateTime.parse(apt['date']);
        return aptDate.isBefore(now);
      } catch (e) {
        return false;
      }
    }).toList();

    if (pastAppointments.isNotEmpty) {
      pastAppointments.sort((a, b) =>
          DateTime.parse(b['date']).compareTo(DateTime.parse(a['date'])));
      return pastAppointments.first['date'];
    }
    return null;
  }

  int _getTotalSessionsForPatient(String patientId) {
    return _appointments.where((apt) => apt['patientId'] == patientId).length;
  }

  void _calculateStatistics() {
    final now = DateTime.now();
    final todayAppointmentsCount = _todayAppointments.length;
    final totalPatients = _patients.length;
    final completedSessions = _appointments.where((apt) {
      try {
        final aptDate = DateTime.parse(apt['date']);
        return aptDate.isBefore(now) && apt['status'] == 'completed';
      } catch (e) {
        return false;
      }
    }).length;

    _statistics = {
      'totalPatients': totalPatients,
      'todayAppointments': todayAppointmentsCount,
      'completedSessions': completedSessions,
      'totalAppointments': _appointments.length,
    };
  }

  Future<void> _logout() async {
    _logger.logLogout(widget.therapistId);

    final prefs = await SharedPreferences.getInstance();
    await prefs.clear();
    if (mounted) {
      Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(builder: (context) => const LoginPage()),
        (route) => false,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        body: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color(0xFF8B5CF6),
                Color(0xFFA855F7),
                Color(0xFFC084FC),
              ],
            ),
          ),
          child: const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  strokeWidth: 3,
                ),
                SizedBox(height: 24),
                Text(
                  'Loading Dashboard...',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'Preparing your personalized experience',
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(
            'Welcome, ${_therapistData?['name']?.split(' ').last ?? 'Doctor'}'),
        elevation: 0,
        backgroundColor: const Color(0xFF8B5CF6),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: () {
              _logger.logInfo(
                category: LogCategory.userInteraction,
                message: 'Accessed activity logs',
                metadata: {'therapistId': widget.therapistId},
              );

              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) =>
                      TherapistLogsScreen(therapistId: widget.therapistId),
                ),
              );
            },
            tooltip: 'Activity Logs',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              _logger.logInfo(
                category: LogCategory.userInteraction,
                message: 'Dashboard refresh requested',
                metadata: {'therapistId': widget.therapistId},
              );
              _loadTherapistData();
            },
            tooltip: 'Refresh',
          ),
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: _logout,
            tooltip: 'Logout',
          ),
        ],
      ),
      drawer: Drawer(
        child: ListView(
          padding: EdgeInsets.zero,
          children: [
            DrawerHeader(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Color(0xFF8B5CF6),
                    Color(0xFFA855F7),
                  ],
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const CircleAvatar(
                    radius: 30,
                    backgroundColor: Colors.white,
                    child:
                        Icon(Icons.person, size: 40, color: Color(0xFF8B5CF6)),
                  ),
                  const SizedBox(height: 10),
                  Text(
                    _therapistData?['name'] ?? 'Dr. Emily Johnson',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                    ),
                  ),
                  Text(
                    _therapistData?['specialization'] ??
                        'Clinical Psychologist',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            ListTile(
              leading: const Icon(Icons.dashboard),
              title: const Text('Dashboard'),
              selected: _selectedIndex == 0,
              onTap: () {
                setState(() {
                  _selectedIndex = 0;
                });
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.people),
              title: const Text('Patients'),
              selected: _selectedIndex == 1,
              onTap: () {
                setState(() {
                  _selectedIndex = 1;
                });
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.calendar_today),
              title: const Text('Appointments'),
              selected: _selectedIndex == 2,
              onTap: () {
                setState(() {
                  _selectedIndex = 2;
                });
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.assignment),
              title: const Text('Tasks'),
              selected: _selectedIndex == 3,
              onTap: () {
                setState(() {
                  _selectedIndex = 3;
                });
                Navigator.pop(context);
              },
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.settings),
              title: const Text('Settings'),
              onTap: () {
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.help),
              title: const Text('Help & Support'),
              onTap: () {
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
      body: _buildBody(),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex,
        onTap: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'Dashboard',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.people),
            label: 'Patients',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.calendar_today),
            label: 'Appointments',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.assignment),
            label: 'Tasks',
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // Add new appointment or task
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildBody() {
    switch (_selectedIndex) {
      case 0:
        return _buildDashboard();
      case 1:
        return _buildPatientsView();
      case 2:
        return _buildAppointmentsView();
      case 3:
        return _buildTasksView();
      default:
        return _buildDashboard();
    }
  }

  Widget _buildDashboard() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Welcome back, ${_therapistData?['name'] ?? 'Dr. Johnson'}',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          Text(
            DateFormat('EEEE, MMMM d, yyyy').format(DateTime.now()),
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 24),

          // Overview Cards
          _buildOverviewCards(),

          const SizedBox(height: 24),

          // Today's Appointments
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "Today's Appointments",
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      TextButton(
                        onPressed: () {},
                        child: const Text('View All'),
                      ),
                    ],
                  ),
                  const Divider(),
                  ..._todayAppointments
                      .map((appointment) => _buildAppointmentItem(appointment)),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Calendar Card
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Calendar',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 16),
                  // SizedBox(
                  //   height: 300,
                  //   child: CalendarCarousel<Event>(
                  //     onDayPressed: (date, events) {
                  //       setState(() {
                  //         _currentDate = date;
                  //       });
                  //     },
                  //     weekendTextStyle: const TextStyle(color: Colors.red),
                  //     thisMonthDayBorderColor: Colors.grey,
                  //     daysHaveCircularBorder: true,
                  //     showOnlyCurrentMonthDate: false,
                  //     weekFormat: false,
                  //     height: 300.0,
                  //     selectedDateTime: _currentDate,
                  //     selectedDayButtonColor: Colors.teal,
                  //     todayButtonColor: Colors.teal.withOpacity(0.3),
                  //     todayBorderColor: Colors.teal,
                  //   ),
                  // ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Tasks Card
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Upcoming Tasks',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      TextButton(
                        onPressed: () {},
                        child: const Text('View All'),
                      ),
                    ],
                  ),
                  const Divider(),
                  const Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Text(
                      'No pending tasks at the moment.',
                      style: TextStyle(
                        color: Colors.grey,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Patient Progress Card
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Patient Progress',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      TextButton(
                        onPressed: () {},
                        child: const Text('View All'),
                      ),
                    ],
                  ),
                  const Divider(),
                  ..._patients
                      .take(3)
                      .map((patient) => _buildPatientProgressItem(patient)),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewCards() {
    if (_isLoading) {
      return GridView.count(
        crossAxisCount: 2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        children: List.generate(4, (index) => _buildLoadingCard()),
      );
    }

    return GridView.count(
      crossAxisCount: 2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      children: [
        _buildOverviewCard(
          title: 'Total Patients',
          value: '${_therapistData?['totalPatients'] ?? _patients.length}',
          icon: Icons.people,
          color: const Color(0xFF8B5CF6),
          subtitle: 'Active patients',
        ),
        _buildOverviewCard(
          title: 'Today\'s Sessions',
          value:
              '${_therapistData?['upcomingAppointments'] ?? _todayAppointments.length}',
          icon: Icons.calendar_today,
          color: const Color(0xFF10B981),
          subtitle: 'Scheduled today',
        ),
        _buildOverviewCard(
          title: 'Completed Sessions',
          value: '${_therapistData?['completedSessions'] ?? 0}',
          icon: Icons.check_circle,
          color: const Color(0xFF3B82F6),
          subtitle: 'Total completed',
        ),
        _buildOverviewCard(
          title: 'Rating',
          value: '${_therapistData?['rating'] ?? 4.9}⭐',
          icon: Icons.star,
          color: const Color(0xFFF59E0B),
          subtitle: 'Patient satisfaction',
        ),
      ],
    );
  }

  Widget _buildOverviewCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    String? subtitle,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 40,
              color: color,
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    color: color,
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
            if (subtitle != null) ...[
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(20),
              ),
            ),
            const SizedBox(height: 8),
            Container(
              height: 24,
              width: 60,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
            const SizedBox(height: 4),
            Container(
              height: 16,
              width: 80,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppointmentItem(Map<String, dynamic> appointment) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.teal.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.access_time,
              color: Colors.teal,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  appointment['patientName'] ?? 'Unknown Patient',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${appointment['time'] ?? '00:00'} | ${appointment['duration'] ?? 60} mins | ${appointment['type'] ?? 'Consultation'}',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            icon: const Icon(Icons.more_vert),
            onPressed: () {},
          ),
        ],
      ),
    );
  }

  Widget _buildTaskItem(Task task) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Checkbox(
            value: task.isCompleted,
            onChanged: (value) {
              setState(() {
                task.isCompleted = value ?? false;
              });
            },
            activeColor: Colors.teal,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  task.title,
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    decoration:
                        task.isCompleted ? TextDecoration.lineThrough : null,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Due: ${_dateFormat.format(task.deadline)}',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            icon: const Icon(Icons.more_vert),
            onPressed: () {},
          ),
        ],
      ),
    );
  }

  Widget _buildPatientProgressItem(Map<String, dynamic> patient) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                backgroundColor: Colors.grey[200],
                child: Text(
                  (patient['name'] ?? 'U').substring(0, 1),
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.teal,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      patient['name'] ?? 'Unknown Patient',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    Text(
                      'Next Appointment: ${patient['nextAppointment'] != null ? _dateFormat.format(DateTime.parse(patient['nextAppointment'])) : 'Not scheduled'}',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: LinearProgressIndicator(
                    value: (patient['totalSessions'] ?? 0) /
                        10.0, // Progress based on sessions
                    backgroundColor: Colors.grey[200],
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.teal),
                    minHeight: 8,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Text(
                '${((patient['totalSessions'] ?? 0) / 10.0 * 100).toInt()}%',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.teal,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPatientsView() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _patients.length,
      itemBuilder: (context, index) {
        final patient = _patients[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 2,
          child: ListTile(
            contentPadding: const EdgeInsets.all(16),
            leading: CircleAvatar(
              backgroundColor: Colors.teal,
              child: Text(
                (patient['name'] ?? 'U').substring(0, 1),
                style: const TextStyle(color: Colors.white),
              ),
            ),
            title: Text(
              patient['name'] ?? 'Unknown Patient',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 4),
                Text('Age: ${patient['age'] ?? 'Unknown'}'),
                Text(
                    'Next appointment: ${patient['nextAppointment'] != null ? _dateFormat.format(DateTime.parse(patient['nextAppointment'])) : 'Not scheduled'}'),
              ],
            ),
            trailing: const Icon(Icons.chevron_right),
            onTap: () {
              // View patient details
            },
          ),
        );
      },
    );
  }

  Widget _buildAppointmentsView() {
    return Column(
      children: [
        // Calendar at the top
        // SizedBox(
        //   height: 350,
        //   child: CalendarCarousel<Event>(
        //     onDayPressed: (date, events) {
        //       setState(() {
        //         _currentDate = date;
        //       });
        //     },
        //     weekendTextStyle: const TextStyle(color: Colors.red),
        //     thisMonthDayBorderColor: Colors.grey,
        //     daysHaveCircularBorder: true,
        //     showOnlyCurrentMonthDate: false,
        //     weekFormat: false,
        //     height: 350.0,
        //     selectedDateTime: _currentDate,
        //     selectedDayButtonColor: Colors.teal,
        //     todayButtonColor: Colors.teal.withOpacity(0.3),
        //     todayBorderColor: Colors.teal,
        //     headerTextStyle: const TextStyle(
        //       fontSize: 20,
        //       fontWeight: FontWeight.bold,
        //       color: Colors.teal,
        //     ),
        //   ),
        // ),

        // Appointments for selected date
        Expanded(
          child: Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Appointments for ${DateFormat('MMMM d, yyyy').format(_currentDate)}',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: ListView.builder(
                    itemCount: _todayAppointments.length,
                    itemBuilder: (context, index) {
                      final appointment = _todayAppointments[index];
                      return Card(
                        margin: const EdgeInsets.only(bottom: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 2,
                        child: ListTile(
                          contentPadding: const EdgeInsets.all(16),
                          leading: Container(
                            width: 50,
                            height: 50,
                            decoration: BoxDecoration(
                              color: Colors.teal.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Center(
                              child: Text(
                                (appointment['time'] ?? '00:00')
                                    .substring(0, 2),
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.teal,
                                  fontSize: 18,
                                ),
                              ),
                            ),
                          ),
                          title: Text(
                            appointment['patientName'] ?? 'Unknown Patient',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const SizedBox(height: 4),
                              Text(
                                  '${appointment['time'] ?? '00:00'} - ${appointment['duration'] ?? 60} mins'),
                              Text(appointment['type'] ?? 'Consultation'),
                            ],
                          ),
                          trailing: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              IconButton(
                                icon:
                                    const Icon(Icons.edit, color: Colors.blue),
                                onPressed: () {},
                              ),
                              IconButton(
                                icon:
                                    const Icon(Icons.delete, color: Colors.red),
                                onPressed: () {},
                              ),
                            ],
                          ),
                          onTap: () {
                            // View appointment details
                          },
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTasksView() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.assignment_outlined,
              size: 80,
              color: Colors.grey,
            ),
            SizedBox(height: 24),
            Text(
              'Task Management',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 16),
            Text(
              'Task management feature will be available soon.\nYou can manage your daily tasks and reminders here.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Models
class Patient {
  final int id;
  final String name;
  final int age;
  final DateTime nextAppointment;
  final double progress;

  Patient({
    required this.id,
    required this.name,
    required this.age,
    required this.nextAppointment,
    required this.progress,
  });
}

class Appointment {
  final int id;
  final String patientName;
  final String time;
  final int duration;
  final String type;

  Appointment({
    required this.id,
    required this.patientName,
    required this.time,
    required this.duration,
    required this.type,
  });
}

class Task {
  final int id;
  final String title;
  final DateTime deadline;
  bool isCompleted;

  Task({
    required this.id,
    required this.title,
    required this.deadline,
    required this.isCompleted,
  });
}
