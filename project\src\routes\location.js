// routes/location.js
const express = require('express');
const router = express.Router();
const locationController = require('../controllers/locationController');

// Update user location
router.post('/updateLocation', locationController.updateLocation);

// Find nearby therapists
router.get('/therapists/nearby', locationController.findNearbyTherapists);

// Get therapist location
router.get('/therapists/:therapistId/location', locationController.getTherapistLocation);

// Get location-based recommendations
router.get('/recommendations/location', locationController.getLocationBasedRecommendations);

module.exports = router;
