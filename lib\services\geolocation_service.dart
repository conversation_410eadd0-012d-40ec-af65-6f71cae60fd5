import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:myapp/api_service.dart';

class GeolocationService {
  static final GeolocationService _instance = GeolocationService._internal();
  factory GeolocationService() => _instance;
  GeolocationService._internal();

  Position? _currentPosition;
  String? _currentAddress;
  bool _isLocationEnabled = false;

  // Get current location
  Future<Position?> getCurrentLocation() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        if (kDebugMode) {
          print('❌ Location services are disabled');
        }
        return null;
      }

      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          if (kDebugMode) {
            print('❌ Location permissions are denied');
          }
          return null;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        if (kDebugMode) {
          print('❌ Location permissions are permanently denied');
        }
        return null;
      }

      // Get current position
      _currentPosition = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      _isLocationEnabled = true;

      if (kDebugMode) {
        print(
            '📍 Current location: ${_currentPosition!.latitude}, ${_currentPosition!.longitude}');
      }

      return _currentPosition;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to get current location: $e');
      }
      return null;
    }
  }

  // Get address from coordinates
  Future<String?> getAddressFromCoordinates(
      double latitude, double longitude) async {
    try {
      List<Placemark> placemarks =
          await placemarkFromCoordinates(latitude, longitude);

      if (placemarks.isNotEmpty) {
        Placemark place = placemarks[0];
        _currentAddress =
            '${place.street}, ${place.locality}, ${place.administrativeArea}, ${place.country}';

        if (kDebugMode) {
          print('📍 Address: $_currentAddress');
        }

        return _currentAddress;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to get address: $e');
      }
    }
    return null;
  }

  // Get coordinates from address
  Future<Position?> getCoordinatesFromAddress(String address) async {
    try {
      List<Location> locations = await locationFromAddress(address);

      if (locations.isNotEmpty) {
        Location location = locations[0];
        return Position(
          latitude: location.latitude,
          longitude: location.longitude,
          timestamp: DateTime.now(),
          accuracy: 0,
          altitude: 0,
          altitudeAccuracy: 0,
          heading: 0,
          headingAccuracy: 0,
          speed: 0,
          speedAccuracy: 0,
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to get coordinates from address: $e');
      }
    }
    return null;
  }

  // Find nearby therapists
  Future<List<Map<String, dynamic>>> findNearbyTherapists({
    double? latitude,
    double? longitude,
    double radius = 10.0, // km
  }) async {
    try {
      // Use current location if coordinates not provided
      if (latitude == null || longitude == null) {
        final position = await getCurrentLocation();
        if (position == null) {
          throw Exception('Unable to get current location');
        }
        latitude = position.latitude;
        longitude = position.longitude;
      }

      // Call API to find nearby therapists
      final result =
          await ApiService.getNearbyTherapists(latitude, longitude, radius);

      if (result['success']) {
        final therapists = result['data'] as List<dynamic>;

        // Calculate distance for each therapist
        List<Map<String, dynamic>> therapistsWithDistance = [];

        for (var therapist in therapists) {
          final therapistData = therapist as Map<String, dynamic>;

          if (therapistData['location'] != null) {
            final therapistLat =
                therapistData['location']['latitude'] as double?;
            final therapistLng =
                therapistData['location']['longitude'] as double?;

            if (therapistLat != null && therapistLng != null) {
              final distance = Geolocator.distanceBetween(
                    latitude,
                    longitude,
                    therapistLat,
                    therapistLng,
                  ) /
                  1000; // Convert to km

              therapistData['distance'] = distance;
              therapistData['distanceText'] =
                  '${distance.toStringAsFixed(1)} km';
              therapistsWithDistance.add(therapistData);
            }
          }
        }

        // Sort by distance
        therapistsWithDistance.sort((a, b) =>
            (a['distance'] as double).compareTo(b['distance'] as double));

        if (kDebugMode) {
          print('🔍 Found ${therapistsWithDistance.length} nearby therapists');
        }

        return therapistsWithDistance;
      } else {
        throw Exception(result['error'] ?? 'Failed to find nearby therapists');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to find nearby therapists: $e');
      }
      return [];
    }
  }

  // Update user location in backend
  Future<bool> updateUserLocation(String userId) async {
    try {
      final position = await getCurrentLocation();
      if (position == null) return false;

      final address = await getAddressFromCoordinates(
        position.latitude,
        position.longitude,
      );

      // TODO: Implement updateUserLocation API call
      // final result = await ApiService.updateUserLocation(
      //   userId: userId,
      //   latitude: position.latitude,
      //   longitude: position.longitude,
      //   address: address,
      // );

      if (kDebugMode) {
        print('✅ User location updated successfully (local only)');
      }
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating user location: $e');
      }
      return false;
    }
  }

  // Calculate distance between two points
  double calculateDistance({
    required double lat1,
    required double lng1,
    required double lat2,
    required double lng2,
  }) {
    return Geolocator.distanceBetween(lat1, lng1, lat2, lng2) / 1000; // km
  }

  // Get location permission status
  Future<LocationPermissionStatus> getLocationPermissionStatus() async {
    final permission = await Geolocator.checkPermission();

    switch (permission) {
      case LocationPermission.always:
      case LocationPermission.whileInUse:
        return LocationPermissionStatus.granted;
      case LocationPermission.denied:
        return LocationPermissionStatus.denied;
      case LocationPermission.deniedForever:
        return LocationPermissionStatus.deniedForever;
      case LocationPermission.unableToDetermine:
        return LocationPermissionStatus.unknown;
    }
  }

  // Request location permission
  Future<bool> requestLocationPermission() async {
    try {
      final permission = await Geolocator.requestPermission();
      return permission == LocationPermission.always ||
          permission == LocationPermission.whileInUse;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to request location permission: $e');
      }
      return false;
    }
  }

  // Open location settings
  Future<void> openLocationSettings() async {
    await Geolocator.openLocationSettings();
  }

  // Start location tracking
  Stream<Position> startLocationTracking() {
    return Geolocator.getPositionStream(
      locationSettings: const LocationSettings(
        accuracy: LocationAccuracy.high,
        distanceFilter: 10, // Update every 10 meters
      ),
    );
  }

  // Get formatted address
  String getFormattedAddress(Placemark placemark) {
    List<String> addressParts = [];

    if (placemark.street?.isNotEmpty == true) {
      addressParts.add(placemark.street!);
    }
    if (placemark.locality?.isNotEmpty == true) {
      addressParts.add(placemark.locality!);
    }
    if (placemark.administrativeArea?.isNotEmpty == true) {
      addressParts.add(placemark.administrativeArea!);
    }
    if (placemark.country?.isNotEmpty == true) {
      addressParts.add(placemark.country!);
    }

    return addressParts.join(', ');
  }

  // Getters
  Position? get currentPosition => _currentPosition;
  String? get currentAddress => _currentAddress;
  bool get isLocationEnabled => _isLocationEnabled;
}

enum LocationPermissionStatus {
  granted,
  denied,
  deniedForever,
  unknown,
}
