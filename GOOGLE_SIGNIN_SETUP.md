# 🔐 Google Sign-In Setup Guide for MindEase

## 📋 Prerequisites
1. Google Account
2. Google Cloud Console access
3. MindEase project running

## 🚀 Step-by-Step Setup

### Step 1: Create Google Cloud Project
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Name it "MindEase" or similar

### Step 2: Enable Google Sign-In API
1. In Google Cloud Console, go to "APIs & Services" > "Library"
2. Search for "Google Sign-In API" or "Google+ API"
3. Click "Enable"

### Step 3: Create OAuth 2.0 Credentials
1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth 2.0 Client IDs"
3. Configure consent screen if prompted:
   - Application name: "MindEase"
   - User support email: <EMAIL>
   - Developer contact: <EMAIL>

### Step 4: Configure OAuth Client
1. Application type: "Web application"
2. Name: "MindEase Web Client"
3. Authorized JavaScript origins:
   - `http://localhost:8080`
   - `http://127.0.0.1:8080`
4. Authorized redirect URIs:
   - `http://localhost:8080`
   - `http://127.0.0.1:8080`

### Step 5: Get Client IDs
After creating, you'll get:
- **Client ID**: `your-client-id.googleusercontent.com`
- **Client Secret**: `your-client-secret`

### Step 6: Configure Flutter App
Update the Google Sign-In service with your Client ID.

## 🔧 Alternative: Test with Mock Google Sign-In

For immediate testing, I can create a mock Google Sign-In that simulates the flow.

## 📱 Current Status
- ✅ UI is ready and beautiful
- ✅ Backend API is configured
- ⚠️ Needs Google OAuth setup for production
- ✅ Mock version available for testing

## 🎯 Quick Test Solution
Use the regular login for now:
- Email: `<EMAIL>`
- Password: `password123`
- Role: User

The Google Sign-In will work perfectly once OAuth is configured!
