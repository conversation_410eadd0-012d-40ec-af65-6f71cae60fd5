{"name": "exit-on-epipe", "version": "1.0.1", "author": "sheetjs", "description": "Cleanly exit process on EPIPE", "keywords": ["epipe", "pipe", "error", "exit"], "main": "./exit-on-epipe", "dependencies": {}, "devDependencies": {"mocha": "~2.5.3"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/node-exit-on-epipe.git"}, "scripts": {"test": "make test"}, "files": ["exit-on-epipe.js", "LICENSE", "README.md"], "bugs": {"url": "https://github.com/SheetJS/node-exit-on-epipe/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}}