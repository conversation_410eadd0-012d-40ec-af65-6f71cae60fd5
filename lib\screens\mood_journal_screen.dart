import 'package:flutter/material.dart';
import 'package:myapp/api_service.dart';
import 'package:myapp/services/semantic_analysis_service.dart';
import 'package:myapp/screens/semantic_analysis_screen.dart';
import 'package:myapp/providers/theme_provider.dart';

class MoodJournalScreen extends StatefulWidget {
  final String userId;

  const MoodJournalScreen({
    super.key,
    required this.userId,
  });

  @override
  State<MoodJournalScreen> createState() => _MoodJournalScreenState();
}

class _MoodJournalScreenState extends State<MoodJournalScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _journalController = TextEditingController();
  final SemanticAnalysisService _semanticService = SemanticAnalysisService();

  String _selectedMood = 'neutral';
  int _moodIntensity = 5;
  bool _isLoading = false;
  List<Map<String, dynamic>> _moodEntries = [];
  List<Map<String, dynamic>> _journalEntries = [];

  final Map<String, String> _moodEmojis = {
    'very_happy': '😄',
    'happy': '😊',
    'neutral': '😐',
    'sad': '😔',
    'very_sad': '😢',
    'angry': '😠',
    'anxious': '😰',
    'excited': '🤩',
    'calm': '😌',
    'stressed': '😫',
  };

  final Map<String, Color> _moodColors = {
    'very_happy': Colors.green,
    'happy': Colors.lightGreen,
    'neutral': Colors.grey,
    'sad': Colors.blue,
    'very_sad': Colors.indigo,
    'angry': Colors.red,
    'anxious': Colors.orange,
    'excited': Colors.purple,
    'calm': Colors.teal,
    'stressed': Colors.deepOrange,
  };

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load mood entries
      final moodResult = await ApiService.getMoodEntries(widget.userId);
      if (moodResult['success']) {
        setState(() {
          _moodEntries =
              List<Map<String, dynamic>>.from(moodResult['data'] ?? []);
        });
      }

      // Load journal entries
      final journalResult = await ApiService.getJournalEntries(widget.userId);
      if (journalResult['success']) {
        setState(() {
          _journalEntries =
              List<Map<String, dynamic>>.from(journalResult['data'] ?? []);
        });
      }
    } catch (e) {
      print('Error loading data: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundLight,
      appBar: AppBar(
        title: const Text(
          'Mood & Journal',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            letterSpacing: -0.5,
          ),
        ),
        backgroundColor: AppTheme.surfaceWhite,
        foregroundColor: AppTheme.textDark,
        elevation: 0,
        shadowColor: Colors.transparent,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: AppTheme.primaryBlue,
          indicatorWeight: 3,
          labelColor: AppTheme.primaryBlue,
          unselectedLabelColor: AppTheme.textLight,
          labelStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 14,
          ),
          unselectedLabelStyle: const TextStyle(
            fontWeight: FontWeight.normal,
            fontSize: 14,
          ),
          tabs: const [
            Tab(icon: Icon(Icons.mood), text: 'Track Mood'),
            Tab(icon: Icon(Icons.book), text: 'Journal'),
            Tab(icon: Icon(Icons.analytics), text: 'History'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildMoodTracker(),
          _buildJournal(),
          _buildHistory(),
        ],
      ),
    );
  }

  Widget _buildMoodTracker() {
    return Container(
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.spacingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(AppTheme.spacingL),
                  decoration: BoxDecoration(
                    color: AppTheme.surfaceWhite,
                    borderRadius:
                        BorderRadius.circular(AppTheme.borderRadiusXL),
                    boxShadow: AppTheme.cardShadow,
                  ),
                  child: Column(
                    children: [
                      Text(
                        'How are you feeling today?',
                        style:
                            Theme.of(context).textTheme.headlineSmall?.copyWith(
                                  color: AppTheme.textDark,
                                  fontWeight: FontWeight.bold,
                                ),
                      ),
                      const SizedBox(height: AppTheme.spacingL),

                      // Mood Selection Grid
                      GridView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        gridDelegate:
                            const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 5,
                          crossAxisSpacing: 8,
                          mainAxisSpacing: 8,
                        ),
                        itemCount: _moodEmojis.length,
                        itemBuilder: (context, index) {
                          final mood = _moodEmojis.keys.elementAt(index);
                          final emoji = _moodEmojis[mood]!;
                          final isSelected = _selectedMood == mood;

                          return GestureDetector(
                            onTap: () {
                              setState(() {
                                _selectedMood = mood;
                              });
                            },
                            child: Container(
                              decoration: BoxDecoration(
                                color: isSelected
                                    ? _moodColors[mood]?.withOpacity(0.2)
                                    : Colors.grey[100],
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: isSelected
                                      ? _moodColors[mood]!
                                      : Colors.grey[300]!,
                                  width: 2,
                                ),
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    emoji,
                                    style: const TextStyle(fontSize: 24),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    mood.replaceAll('_', ' '),
                                    style: TextStyle(
                                      fontSize: 8,
                                      fontWeight: isSelected
                                          ? FontWeight.bold
                                          : FontWeight.normal,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),

                      const SizedBox(height: 20),

                      // Intensity Slider
                      const Text(
                        'Intensity Level',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Slider(
                        value: _moodIntensity.toDouble(),
                        min: 1,
                        max: 10,
                        divisions: 9,
                        label: _moodIntensity.toString(),
                        activeColor: _moodColors[_selectedMood],
                        onChanged: (value) {
                          setState(() {
                            _moodIntensity = value.round();
                          });
                        },
                      ),
                      Text(
                        'Level: $_moodIntensity/10',
                        style: const TextStyle(fontSize: 14),
                      ),

                      const SizedBox(height: 20),

                      // Save Button
                      ElevatedButton(
                        onPressed: _isLoading ? null : _saveMoodEntry,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: _moodColors[_selectedMood],
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: _isLoading
                            ? const CircularProgressIndicator(
                                color: Colors.white)
                            : const Text(
                                'Save Mood Entry',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildJournal() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF4CAF50),
            Color(0xFF66BB6A),
            Color(0xFF81C784),
          ],
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Card(
                elevation: 8,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Write in your journal',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      TextField(
                        controller: _journalController,
                        maxLines: 8,
                        decoration: const InputDecoration(
                          hintText:
                              'Share your thoughts, feelings, and experiences...',
                          border: OutlineInputBorder(),
                          filled: true,
                          fillColor: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: _isLoading ? null : _saveJournalEntry,
                              icon: const Icon(Icons.save),
                              label: const Text('Save Entry'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.green,
                                foregroundColor: Colors.white,
                                padding:
                                    const EdgeInsets.symmetric(vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          ElevatedButton.icon(
                            onPressed: _analyzeJournalEntry,
                            icon: const Icon(Icons.psychology),
                            label: const Text('AI Analysis'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.purple,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHistory() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF2196F3),
            Color(0xFF42A5F5),
            Color(0xFF64B5F6),
          ],
        ),
      ),
      child: SafeArea(
        child: _isLoading
            ? const Center(
                child: CircularProgressIndicator(color: Colors.white))
            : RefreshIndicator(
                onRefresh: _loadData,
                child: ListView(
                  padding: const EdgeInsets.all(16.0),
                  children: [
                    // Recent Mood Entries
                    Card(
                      elevation: 4,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Recent Mood Entries',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 12),
                            if (_moodEntries.isEmpty)
                              const Text(
                                  'No mood entries yet. Start tracking your mood!')
                            else
                              ..._moodEntries.take(5).map((entry) {
                                final mood = entry['mood'] ?? 'neutral';
                                final emoji = _moodEmojis[mood] ?? '😐';
                                final date =
                                    DateTime.tryParse(entry['date'] ?? '') ??
                                        DateTime.now();

                                return ListTile(
                                  leading: Text(emoji,
                                      style: const TextStyle(fontSize: 24)),
                                  title: Text(
                                      mood.replaceAll('_', ' ').toUpperCase()),
                                  subtitle: Text(
                                      '${date.day}/${date.month}/${date.year}'),
                                  trailing: entry['intensity'] != null
                                      ? Chip(
                                          label:
                                              Text('${entry['intensity']}/10'),
                                          backgroundColor: _moodColors[mood]
                                              ?.withOpacity(0.2),
                                        )
                                      : null,
                                );
                              }).toList(),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Recent Journal Entries
                    Card(
                      elevation: 4,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Recent Journal Entries',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 12),
                            if (_journalEntries.isEmpty)
                              const Text(
                                  'No journal entries yet. Start writing!')
                            else
                              ..._journalEntries.take(3).map((entry) {
                                final date =
                                    DateTime.tryParse(entry['date'] ?? '') ??
                                        DateTime.now();
                                final content = entry['content'] ?? '';

                                return Card(
                                  margin: const EdgeInsets.only(bottom: 8),
                                  child: Padding(
                                    padding: const EdgeInsets.all(12),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          '${date.day}/${date.month}/${date.year}',
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                            color: Colors.grey,
                                          ),
                                        ),
                                        const SizedBox(height: 4),
                                        Text(
                                          content.length > 100
                                              ? '${content.substring(0, 100)}...'
                                              : content,
                                          style: const TextStyle(fontSize: 14),
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              }).toList(),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
      ),
    );
  }

  Future<void> _saveMoodEntry() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final result = await ApiService.saveMoodEntry(
        widget.userId,
        _selectedMood,
        'Intensity: $_moodIntensity/10',
      );

      if (result['success']) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Mood entry saved successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        await _loadData();
      } else {
        throw Exception(result['error'] ?? 'Failed to save mood entry');
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _saveJournalEntry() async {
    final content = _journalController.text.trim();

    if (content.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please write something in your journal'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final result = await ApiService.saveJournalEntry(
        widget.userId,
        'Journal Entry - ${DateTime.now().toString().split(' ')[0]}',
        content,
      );

      if (result['success']) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Journal entry saved successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        _journalController.clear();
        await _loadData();
      } else {
        throw Exception(result['error'] ?? 'Failed to save journal entry');
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _analyzeJournalEntry() {
    final content = _journalController.text.trim();

    if (content.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please write something to analyze'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SemanticAnalysisScreen(userId: widget.userId),
      ),
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    _journalController.dispose();
    super.dispose();
  }
}
