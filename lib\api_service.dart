import 'dart:convert';
import 'package:http/http.dart' as http;

class ApiService {
  static const String baseUrl = 'http://localhost:3000/api';

  // Headers for all requests
  static Map<String, String> get headers => {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      };

  // Generic error handling
  static Map<String, dynamic> _handleError(String operation, dynamic error) {
    print('❌ API Error in $operation: $error');
    return {
      'success': false,
      'error': 'Network error occurred. Please check your connection.',
      'details': error.toString(),
    };
  }

  // Generic response handler
  static Map<String, dynamic> _handleResponse(
      http.Response response, String operation) {
    print('📡 API Response for $operation: ${response.statusCode}');

    if (response.statusCode >= 200 && response.statusCode < 300) {
      try {
        final data = jsonDecode(response.body);
        return {
          'success': true,
          'data': data,
          'statusCode': response.statusCode,
        };
      } catch (e) {
        return {
          'success': true,
          'data': response.body,
          'statusCode': response.statusCode,
        };
      }
    } else {
      try {
        final errorData = jsonDecode(response.body);
        return {
          'success': false,
          'error':
              errorData['error'] ?? errorData['message'] ?? 'Unknown error',
          'statusCode': response.statusCode,
        };
      } catch (e) {
        return {
          'success': false,
          'error': 'Server error occurred',
          'statusCode': response.statusCode,
        };
      }
    }
  }

  // ==================== USER MANAGEMENT ====================

  static Future<Map<String, dynamic>> login(
      String email, String password, String userType) async {
    try {
      final endpoint = userType == 'user' ? 'users' : 'therapists';
      final response = await http.post(
        Uri.parse('$baseUrl/$endpoint/login'),
        headers: headers,
        body: jsonEncode({
          'email': email,
          'password': password,
          'role': userType, // Add role parameter for backend
        }),
      );
      return _handleResponse(response, 'Login');
    } catch (e) {
      return _handleError('Login', e);
    }
  }

  static Future<Map<String, dynamic>> signup(
      String username, String email, String password, String role) async {
    try {
      // Use different endpoints for users and therapists
      String endpoint;
      Map<String, dynamic> requestBody;

      if (role == 'therapist') {
        endpoint = 'therapists';
        // Therapist signup requires additional fields
        requestBody = {
          'name': username, // Therapist uses 'name' instead of 'username'
          'email': email,
          'password': password,
          'role': role,
          'specialty': 'General', // Default specialty
          'location': 'Not specified', // Default location
        };
      } else {
        endpoint = 'users/signup';
        requestBody = {
          'username': username,
          'email': email,
          'password': password,
          'role': role,
        };
      }

      final response = await http.post(
        Uri.parse('$baseUrl/$endpoint'),
        headers: headers,
        body: jsonEncode(requestBody),
      );
      return _handleResponse(response, 'Signup');
    } catch (e) {
      return _handleError('Signup', e);
    }
  }

  static Future<Map<String, dynamic>> getUserById(String userId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/users/$userId'),
        headers: headers,
      );
      return _handleResponse(response, 'Get User');
    } catch (e) {
      return _handleError('Get User', e);
    }
  }

  static Future<Map<String, dynamic>> forgotPassword(String email) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/users/forgot-password'),
        headers: headers,
        body: jsonEncode({'email': email}),
      );
      return _handleResponse(response, 'Forgot Password');
    } catch (e) {
      return _handleError('Forgot Password', e);
    }
  }

  // ==================== MOOD TRACKING ====================

  static Future<Map<String, dynamic>> saveMoodEntry(
      String userId, String mood, String? note) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/mood/entry'),
        headers: headers,
        body: jsonEncode({
          'userId': userId,
          'mood': mood,
          'note': note ?? '',
        }),
      );
      return _handleResponse(response, 'Save Mood Entry');
    } catch (e) {
      return _handleError('Save Mood Entry', e);
    }
  }

  static Future<Map<String, dynamic>> getMoodEntries(String userId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/mood/$userId'),
        headers: headers,
      );
      return _handleResponse(response, 'Get Mood Entries');
    } catch (e) {
      return _handleError('Get Mood Entries', e);
    }
  }

  // ==================== JOURNALING ====================

  static Future<Map<String, dynamic>> saveJournalEntry(
      String userId, String title, String content) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/journal/create'),
        headers: headers,
        body: jsonEncode({
          'userId': userId,
          'title': title,
          'content': content,
        }),
      );
      return _handleResponse(response, 'Save Journal Entry');
    } catch (e) {
      return _handleError('Save Journal Entry', e);
    }
  }

  static Future<Map<String, dynamic>> getJournalEntries(String userId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/journal?userId=$userId'),
        headers: headers,
      );
      return _handleResponse(response, 'Get Journal Entries');
    } catch (e) {
      return _handleError('Get Journal Entries', e);
    }
  }

  // ==================== THERAPISTS ====================

  static Future<Map<String, dynamic>> getAllTherapists() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/therapists'),
        headers: headers,
      );
      return _handleResponse(response, 'Get All Therapists');
    } catch (e) {
      return _handleError('Get All Therapists', e);
    }
  }

  static Future<Map<String, dynamic>> getNearbyTherapists(
      double lat, double lng, double radius) async {
    try {
      final response = await http.get(
        Uri.parse(
            '$baseUrl/location/therapists/nearby?lat=$lat&lng=$lng&radius=$radius'),
        headers: headers,
      );
      return _handleResponse(response, 'Get Nearby Therapists');
    } catch (e) {
      return _handleError('Get Nearby Therapists', e);
    }
  }

  // ==================== APPOINTMENTS ====================

  static Future<Map<String, dynamic>> bookAppointment(
      Map<String, dynamic> appointmentData) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/appointments'),
        headers: headers,
        body: jsonEncode(appointmentData),
      );
      return _handleResponse(response, 'Book Appointment');
    } catch (e) {
      return _handleError('Book Appointment', e);
    }
  }

  static Future<Map<String, dynamic>> getUserAppointments(String userId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/appointments/user/$userId'),
        headers: headers,
      );
      return _handleResponse(response, 'Get User Appointments');
    } catch (e) {
      return _handleError('Get User Appointments', e);
    }
  }

  // ==================== PAYMENTS ====================

  static Future<Map<String, dynamic>> createPaymentIntent(
      Map<String, dynamic> paymentData) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/payments/create-intent'),
        headers: headers,
        body: jsonEncode(paymentData),
      );
      return _handleResponse(response, 'Create Payment Intent');
    } catch (e) {
      return _handleError('Create Payment Intent', e);
    }
  }

  static Future<Map<String, dynamic>> getPaymentHistory(String userId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/payments/history/$userId'),
        headers: headers,
      );
      return _handleResponse(response, 'Get Payment History');
    } catch (e) {
      return _handleError('Get Payment History', e);
    }
  }

  // ==================== SEMANTIC ANALYSIS ====================

  static Future<Map<String, dynamic>> analyzeMood(String text) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/semantic/mood-analysis'),
        headers: headers,
        body: jsonEncode({'text': text}),
      );
      return _handleResponse(response, 'Analyze Mood');
    } catch (e) {
      return _handleError('Analyze Mood', e);
    }
  }

  static Future<Map<String, dynamic>> analyzeJournal(
      String userId, String content) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/semantic/analyze-journal'),
        headers: headers,
        body: jsonEncode({'userId': userId, 'content': content}),
      );
      return _handleResponse(response, 'Analyze Journal');
    } catch (e) {
      return _handleError('Analyze Journal', e);
    }
  }

  static Future<Map<String, dynamic>> getMoodTrends(String userId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/semantic/mood-trends/$userId'),
        headers: headers,
      );
      return _handleResponse(response, 'Get Mood Trends');
    } catch (e) {
      return _handleError('Get Mood Trends', e);
    }
  }

  // ==================== VIDEO CALL APIs ====================

  // Create Zoom meeting
  static Future<Map<String, dynamic>> createZoomMeeting({
    required String topic,
    required String startTime,
    required int duration,
    required String hostId,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/zoom/create-meeting'),
        headers: headers,
        body: jsonEncode({
          'topic': topic,
          'start_time': startTime,
          'duration': duration,
          'host_id': hostId,
        }),
      );
      return _handleResponse(response, 'Create Zoom Meeting');
    } catch (e) {
      return _handleError('Create Zoom Meeting', e);
    }
  }

  // Join Zoom meeting
  static Future<Map<String, dynamic>> joinZoomMeeting(String meetingId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/zoom/join/$meetingId'),
        headers: headers,
      );
      return _handleResponse(response, 'Join Zoom Meeting');
    } catch (e) {
      return _handleError('Join Zoom Meeting', e);
    }
  }

  // Get Agora token for video call
  static Future<Map<String, dynamic>> getAgoraToken({
    required String channelName,
    required String userId,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/zoom/agora-token'),
        headers: headers,
        body: jsonEncode({
          'channelName': channelName,
          'userId': userId,
        }),
      );
      return _handleResponse(response, 'Get Agora Token');
    } catch (e) {
      return _handleError('Get Agora Token', e);
    }
  }

  // ==================== LOCATION ====================

  static Future<Map<String, dynamic>> updateUserLocation({
    required String userId,
    required double latitude,
    required double longitude,
    String? address,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/location/update'),
        headers: headers,
        body: jsonEncode({
          'userId': userId,
          'latitude': latitude,
          'longitude': longitude,
          'address': address,
        }),
      );

      if (response.statusCode == 200) {
        return {'success': true, 'data': jsonDecode(response.body)};
      } else {
        return _handleError(
            'Update User Location', 'HTTP ${response.statusCode}');
      }
    } catch (e) {
      return _handleError('Update User Location', e);
    }
  }
}
