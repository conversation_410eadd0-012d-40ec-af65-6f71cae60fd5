const express = require('express');
const router = express.Router();
const userRoutes = require('./userRoutes');
const journalRoutes = require('./journalRoutes');
const paymentRoutes = require('./paymentRoutes');
const therapistRoutes = require('./therapistRoutes');
const postRoutes = require('./postRoutes');
const appointmentRoutes = require('./appointmentRoutes');
const locationRoutes = require('./location');
const semanticRoutes = require('./semanticRoutes');
const zoomRoutes = require('./zoom');
const databaseTestRoutes = require('./database-test');

router.use('/users', userRoutes);
router.use('/mood', postRoutes);
router.use('/journal', journalRoutes);
router.use('/payments', paymentRoutes);
router.use('/therapists', therapistRoutes);
router.use('/appointments', appointmentRoutes);
router.use('/location', locationRoutes);
router.use('/semantic', semanticRoutes);
router.use('/zoom', zoomRoutes);
router.use('/db-test', databaseTestRoutes);

module.exports = router;