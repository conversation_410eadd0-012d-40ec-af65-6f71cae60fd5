import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:myapp/admin.dart';
import 'package:myapp/analysis.dart';
import 'package:myapp/appointment.dart';
import 'package:myapp/components/onboarding_screen.dart';
import 'package:myapp/forget-password.dart';
import 'package:myapp/journaling.dart';
import 'package:myapp/login_page.dart';
import 'package:myapp/mood_tracking.dart';
import 'package:myapp/settings.dart';
import 'package:myapp/signup_page.dart';
import 'package:myapp/therapist.dart';
import 'package:myapp/therapist_dashboard.dart';
import 'package:myapp/screens/beautiful_home_screen.dart';
// import 'package:myapp/screens/video_call_screen.dart';  // Temporarily disabled
import 'package:myapp/screens/payment_screen.dart';
import 'package:myapp/providers/theme_provider.dart';
import 'package:myapp/providers/language_provider.dart';
import 'package:myapp/test_therapist_login.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize app (simplified for demo)
  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => ThemeProvider()),
        ChangeNotifierProvider(create: (context) => LanguageProvider()),
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return MaterialApp(
          title: 'MindEase',
          debugShowCheckedModeBanner: false,
          theme: themeProvider.appTheme,
          // home: MyAppointmentsScreen(userId: '680aad8bdef6a277563a942c'), // Replace with your user ID and type
          initialRoute: '/', // Initial route to load
          routes: {
            '/': (context) => const OnboardingScreen(),
            '/login': (context) => const LoginPage(),
            '/signup': (context) => const SignupPage(),
            '/admin': (context) => const AdminScreen(),
            '/therapist': (context) => const TherapistScreen(),
            '/mood_tracking': (context) => const MoodTrackingScreen(),
            '/journaling': (context) => JournalScreen(userId: ''),
            '/analysis': (context) => const AnxietyDepressionTestScreen(),
            '/onboarding': (context) => const OnboardingScreen(),
            '/settings': (context) => const SettingsScreen(),
          },
          onGenerateRoute: (settings) {
            // Handle dynamic routes with parameters
            if (settings.name?.startsWith('/home/') == true) {
              final userId = settings.name!.split('/')[2];
              return MaterialPageRoute(
                builder: (context) => BeautifulHomeScreen(userId: userId),
              );
            }
            return null;
          },
        );
      },
    );
  }
}
