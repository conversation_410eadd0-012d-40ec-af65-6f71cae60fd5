import 'package:flutter/material.dart';
import 'package:myapp/api_service.dart';
import 'package:myapp/providers/theme_provider.dart';

class LocationScreen extends StatefulWidget {
  final String userId;
  
  const LocationScreen({super.key, required this.userId});

  @override
  State<LocationScreen> createState() => _LocationScreenState();
}

class _LocationScreenState extends State<LocationScreen>
    with TickerProviderStateMixin {
  List<Map<String, dynamic>> therapists = [];
  bool isLoading = true;
  String searchQuery = '';
  
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  // Sample therapist data
  final List<Map<String, dynamic>> sampleTherapists = [
    {
      'name': 'Dr. <PERSON>',
      'specialization': 'Anxiety & Depression',
      'rating': 4.9,
      'experience': '8 years',
      'location': 'Downtown Medical Center',
      'distance': '2.3 km',
      'price': '\$120/session',
      'available': true,
      'nextSlot': 'Today 3:00 PM',
    },
    {
      'name': 'Dr. <PERSON>',
      'specialization': 'Cognitive Behavioral Therapy',
      'rating': 4.8,
      'experience': '12 years',
      'location': 'Wellness Psychology Clinic',
      'distance': '3.1 km',
      'price': '\$150/session',
      'available': true,
      'nextSlot': 'Tomorrow 10:00 AM',
    },
    {
      'name': 'Dr. Emily Rodriguez',
      'specialization': 'Trauma & PTSD',
      'rating': 4.9,
      'experience': '10 years',
      'location': 'Mind & Body Therapy Center',
      'distance': '1.8 km',
      'price': '\$140/session',
      'available': false,
      'nextSlot': 'Next week',
    },
  ];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadTherapists();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );
    
    _fadeController.forward();
  }

  Future<void> _loadTherapists() async {
    try {
      final result = await ApiService.getAllTherapists();
      if (result['success'] && result['data']['therapists'] != null) {
        therapists = List<Map<String, dynamic>>.from(result['data']['therapists']);
      }
      
      if (therapists.isEmpty) {
        therapists = sampleTherapists;
      }
      
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          therapists = sampleTherapists;
          isLoading = false;
        });
      }
    }
  }

  List<Map<String, dynamic>> get filteredTherapists {
    if (searchQuery.isEmpty) return therapists;
    return therapists.where((therapist) {
      final name = therapist['name']?.toString().toLowerCase() ?? '';
      final specialization = therapist['specialization']?.toString().toLowerCase() ?? '';
      final query = searchQuery.toLowerCase();
      return name.contains(query) || specialization.contains(query);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.primaryGradient,
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(),
              Expanded(
                child: Container(
                  margin: const EdgeInsets.only(top: 20),
                  decoration: const BoxDecoration(
                    color: AppTheme.backgroundLight,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(30),
                      topRight: Radius.circular(30),
                    ),
                  ),
                  child: Column(
                    children: [
                      _buildSearchBar(),
                      Expanded(
                        child: isLoading ? _buildLoadingState() : _buildTherapistsList(),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.arrow_back, color: Colors.white),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Find Therapists',
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                Text(
                  'Connect with qualified mental health professionals',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.location_on,
              color: Colors.white,
              size: 24,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: TextField(
        onChanged: (value) {
          setState(() {
            searchQuery = value;
          });
        },
        decoration: InputDecoration(
          hintText: 'Search therapists or specializations...',
          prefixIcon: const Icon(Icons.search, color: AppTheme.textMedium),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: AppTheme.surfaceWhite,
          contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: AppTheme.primaryBlue),
          SizedBox(height: 16),
          Text(
            'Finding therapists near you...',
            style: TextStyle(color: AppTheme.textMedium),
          ),
        ],
      ),
    );
  }

  Widget _buildTherapistsList() {
    final filtered = filteredTherapists;
    
    if (filtered.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 64, color: AppTheme.textLight),
            SizedBox(height: 16),
            Text(
              'No therapists found',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: AppTheme.textMedium,
              ),
            ),
            Text(
              'Try adjusting your search criteria',
              style: TextStyle(color: AppTheme.textLight),
            ),
          ],
        ),
      );
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 24),
        itemCount: filtered.length,
        itemBuilder: (context, index) {
          final therapist = filtered[index];
          return _buildTherapistCard(therapist);
        },
      ),
    );
  }

  Widget _buildTherapistCard(Map<String, dynamic> therapist) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.surfaceWhite,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 30,
                backgroundColor: AppTheme.primaryBlue.withOpacity(0.1),
                child: Text(
                  (therapist['name'] ?? 'T')[0].toUpperCase(),
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryBlue,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      therapist['name'] ?? 'Unknown',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textDark,
                      ),
                    ),
                    Text(
                      therapist['specialization'] ?? 'General Therapy',
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppTheme.primaryBlue,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        const Icon(Icons.star, size: 16, color: Colors.amber),
                        const SizedBox(width: 4),
                        Text(
                          '${therapist['rating'] ?? 4.5}',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: AppTheme.textDark,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          '${therapist['experience'] ?? '5 years'} experience',
                          style: const TextStyle(
                            fontSize: 12,
                            color: AppTheme.textMedium,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: (therapist['available'] == true)
                      ? AppTheme.successGreen.withOpacity(0.1)
                      : AppTheme.warningOrange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  (therapist['available'] == true) ? 'Available' : 'Busy',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: (therapist['available'] == true)
                        ? AppTheme.successGreen
                        : AppTheme.warningOrange,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(Icons.location_on, size: 16, color: AppTheme.textMedium),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            therapist['location'] ?? 'Location not specified',
                            style: const TextStyle(
                              fontSize: 12,
                              color: AppTheme.textMedium,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        const Icon(Icons.access_time, size: 16, color: AppTheme.textMedium),
                        const SizedBox(width: 4),
                        Text(
                          therapist['nextSlot'] ?? 'Schedule available',
                          style: const TextStyle(
                            fontSize: 12,
                            color: AppTheme.textMedium,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    therapist['price'] ?? '\$100/session',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.primaryBlue,
                    ),
                  ),
                  const SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: () {
                      _bookAppointment(therapist);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryBlue,
                      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                    ),
                    child: const Text(
                      'Book Now',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _bookAppointment(Map<String, dynamic> therapist) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Book Appointment with ${therapist['name']}'),
        content: Text('Would you like to book an appointment for ${therapist['nextSlot']}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Appointment booked with ${therapist['name']}!'),
                  backgroundColor: AppTheme.successGreen,
                ),
              );
            },
            child: const Text('Confirm'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }
}
