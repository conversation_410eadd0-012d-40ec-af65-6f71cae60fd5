const express = require('express');
const router = express.Router();
const { validateUser } = require('../middleware/validators');
const UserController = require('../controllers/userController');
const ForgotPasswordController = require('../controllers/forgotPasswordController');
const GoogleAuthController = require('../controllers/googleAuthController');

router.get('/', UserController.getAllUsers);
router.post('/forgot-password', ForgotPasswordController.forgotPassword);
router.put('/change-password', UserController.changePassword);
router.get('/:id', UserController.getUserById);
router.post('/signup', UserController.createUser);
router.post('/login', UserController.login);
router.post('/google-login', GoogleAuthController.googleLogin);
router.put('/:id', validateUser, UserController.updateUser);
router.delete('/:id', UserController.deleteUser);
router.post('/:id/mood', UserController.addMoodEntry);
router.get('/:id/recommendations', UserController.getRecommendations);

module.exports = router;