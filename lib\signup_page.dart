// import 'package:flutter/material.dart';
// import 'package:myapp/login_page.dart';
// import 'package:myapp/therapist_screen.dart';
// import 'services/api_service.dart';

// class SignupPage extends StatefulWidget {
//   const SignupPage({super.key});

//   @override
//   _SignupPageState createState() => _SignupPageState();
// }

// class _SignupPageState extends State<SignupPage> {
//   final _formKey = GlobalKey<FormState>();
//   final passwordController = TextEditingController();
//   final passwordConfirmController = TextEditingController();
//   final usernameController = TextEditingController();
//   final emailController = TextEditingController();

//   String userType = "User";
//   bool isPasswordVisible = false;
//   bool isConfirmPasswordVisible = false;

//   @override
//   void dispose() {
//     passwordController.dispose();
//     passwordConfirmController.dispose();
//     usernameController.dispose();
//     emailController.dispose();
//     super.dispose();
//   }

//   void validateAndSignup() async {
//     if (!_formKey.currentState!.validate()) return;

//     final username = usernameController.text.trim();
//     final email = emailController.text.trim();
//     final password = passwordController.text.trim();

//     final result = await ApiService.signup(username, email, password, userType);

//     if (result['success']) {
//       ScaffoldMessenger.of(context).showSnackBar(
//         const SnackBar(content: Text('Signup successful!')),
//       );
//       Navigator.pushReplacement(
//         context,
//         MaterialPageRoute(builder: (context) => const LoginPage()),
//       );
//     } else {
//       showAlert("Signup Error", result['message']);
//     }
//   }

//   void showAlert(String title, String message) {
//     showDialog(
//       context: context,
//       builder: (context) {
//         return AlertDialog(
//           title: Text(title),
//           content: Text(message),
//           actions: [
//             TextButton(
//               onPressed: () => Navigator.of(context).pop(),
//               child: const Text("OK"),
//             ),
//           ],
//         );
//       },
//     );
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       body: Stack(
//         children: [
//           // Background Gradient
//           Container(
//             width: double.infinity,
//             height: double.infinity,
//             decoration: const BoxDecoration(
//               gradient: LinearGradient(
//                 begin: Alignment.topLeft,
//                 end: Alignment.bottomRight,
//                 colors: [
//                   Color.fromRGBO(255, 226, 159, 1), // Light yellow
//                   Color(0xFFFFC0CB), // Light pink
//                 ],
//               ),
//             ),
//           ),
//           SingleChildScrollView(
//             padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
//             child: Form(
//               key: _formKey,
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.stretch,
//                 children: [
//                   const SizedBox(height: 80), // Space for better alignment
//                   _header(),
//                   const SizedBox(height: 20),
//                   _inputFields(),
//                   const SizedBox(height: 20),
//                   _signupButton(),
//                   const SizedBox(height: 20),
//                   _alreadyHaveAccount(),
//                 ],
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _header() {
//     return Column(
//       children: const [
//         Text(
//           "Create Your Account",
//           style: TextStyle(
//             fontSize: 28,
//             fontWeight: FontWeight.bold,
//             color: Colors.black,
//           ),
//           textAlign: TextAlign.center,
//         ),
//         SizedBox(height: 8),
//         Text(
//           "Sign up to join our community",
//           style: TextStyle(fontSize: 16, color: Colors.black54),
//           textAlign: TextAlign.center,
//         ),
//       ],
//     );
//   }

//   // Widget _inputFields() {
//   //   return Column(
//   //     crossAxisAlignment: CrossAxisAlignment.stretch,
//   //     children: [
//   //       DropdownButtonFormField<String>(
//   //         value: userType,
//   //         decoration: InputDecoration(
//   //           filled: true,
//   //           fillColor: Colors.grey[200],
//   //           border: OutlineInputBorder(
//   //             borderRadius: BorderRadius.circular(30),
//   //             borderSide: BorderSide.none,
//   //           ),
//   //         ),
//   //         items: ["User", "Therapist"]
//   //             .map((type) => DropdownMenuItem(value: type, child: Text(type)))
//   //             .toList(),
//   //         onChanged: (value) {
//   //           setState(() {
//   //             userType = value!;
//   //           });
//   //         },
//   //       ),
//   //       const SizedBox(height: 20),
//   //       TextFormField(
//   //         controller: usernameController,
//   //         decoration: const InputDecoration(
//   //           hintText: "Username",
//   //           prefixIcon: Icon(Icons.person, color: Colors.purple),
//   //         ),
//   //         validator: (value) =>
//   //             value == null || value.length < 3 ? "Username must be at least 3 characters" : null,
//   //       ),
//   //       const SizedBox(height: 20),
//   //       TextFormField(
//   //         controller: emailController,
//   //         decoration: const InputDecoration(
//   //           hintText: "Email",
//   //           prefixIcon: Icon(Icons.email, color: Colors.purple),
//   //         ),
//   //         validator: (value) {
//   //           final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+');
//   //           return value == null || !emailRegex.hasMatch(value) ? "Enter a valid email address" : null;
//   //         },
//   //       ),
//   //       const SizedBox(height: 20),
//   //       TextFormField(
//   //         controller: passwordController,
//   //         obscureText: !isPasswordVisible,
//   //         decoration: InputDecoration(
//   //           hintText: "Password",
//   //           prefixIcon: const Icon(Icons.lock, color: Colors.purple),
//   //           suffixIcon: IconButton(
//   //             icon: Icon(
//   //               isPasswordVisible ? Icons.visibility : Icons.visibility_off,
//   //               color: Colors.purple,
//   //             ),
//   //             onPressed: () {
//   //               setState(() {
//   //                 isPasswordVisible = !isPasswordVisible;
//   //               });
//   //             },
//   //           ),
//   //         ),
//   //         validator: (value) =>
//   //             value == null || value.length < 6 ? "Password must be at least 6 characters long" : null,
//   //       ),
//   //       const SizedBox(height: 20),
//   //       TextFormField(
//   //         controller: passwordConfirmController,
//   //         obscureText: !isConfirmPasswordVisible,
//   //         decoration: InputDecoration(
//   //           hintText: "Confirm Password",
//   //           prefixIcon: const Icon(Icons.lock, color: Colors.purple),
//   //           suffixIcon: IconButton(
//   //             icon: Icon(
//   //               isConfirmPasswordVisible ? Icons.visibility : Icons.visibility_off,
//   //               color: Colors.purple,
//   //             ),
//   //             onPressed: () {
//   //               setState(() {
//   //                 isConfirmPasswordVisible = !isConfirmPasswordVisible;
//   //               });
//   //             },
//   //           ),
//   //         ),
//   //         validator: (value) =>
//   //             value != passwordController.text ? "Passwords do not match" : null,
//   //       ),
//   //     ],
//   //   );
//   // }
//   Widget _inputFields() {
//   return Column(
//     crossAxisAlignment: CrossAxisAlignment.stretch,
//     children: [
//       DropdownButtonFormField<String>(
//         value: userType,
//         decoration: InputDecoration(
//           filled: true,
//           fillColor: Colors.grey[200],
//           border: OutlineInputBorder(
//             borderRadius: BorderRadius.circular(30),
//             borderSide: BorderSide.none,
//           ),
//         ),
//         items: ["User", "Therapist"]
//             .map((type) => DropdownMenuItem(value: type, child: Text(type)))
//             .toList(),
//         onChanged: (value) {
//           setState(() {
//             userType = value!;
//           });

//           // Redirect to a new screen when "Therapist" is selected
//           if (value == "Therapist") {
//             Navigator.push(
//               context,
//               MaterialPageRoute(builder: (context) => CreateTherapistPage()), // Replace TherapistScreen with your actual screen widget
//             );
//           }
//         },
//       ),
//       const SizedBox(height: 20),
//       TextFormField(
//         controller: usernameController,
//         decoration: const InputDecoration(
//           hintText: "Username",
//           prefixIcon: Icon(Icons.person, color: Colors.purple),
//         ),
//         validator: (value) =>
//             value == null || value.length < 3 ? "Username must be at least 3 characters" : null,
//       ),
//       const SizedBox(height: 20),
//       TextFormField(
//         controller: emailController,
//         decoration: const InputDecoration(
//           hintText: "Email",
//           prefixIcon: Icon(Icons.email, color: Colors.purple),
//         ),
//         validator: (value) {
//           final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+');
//           return value == null || !emailRegex.hasMatch(value) ? "Enter a valid email address" : null;
//         },
//       ),
//       const SizedBox(height: 20),
//       TextFormField(
//         controller: passwordController,
//         obscureText: !isPasswordVisible,
//         decoration: InputDecoration(
//           hintText: "Password",
//           prefixIcon: const Icon(Icons.lock, color: Colors.purple),
//           suffixIcon: IconButton(
//             icon: Icon(
//               isPasswordVisible ? Icons.visibility : Icons.visibility_off,
//               color: Colors.purple,
//             ),
//             onPressed: () {
//               setState(() {
//                 isPasswordVisible = !isPasswordVisible;
//               });
//             },
//           ),
//         ),
//         validator: (value) =>
//             value == null || value.length < 6 ? "Password must be at least 6 characters long" : null,
//       ),
//       const SizedBox(height: 20),
//       TextFormField(
//         controller: passwordConfirmController,
//         obscureText: !isConfirmPasswordVisible,
//         decoration: InputDecoration(
//           hintText: "Confirm Password",
//           prefixIcon: const Icon(Icons.lock, color: Colors.purple),
//           suffixIcon: IconButton(
//             icon: Icon(
//               isConfirmPasswordVisible ? Icons.visibility : Icons.visibility_off,
//               color: Colors.purple,
//             ),
//             onPressed: () {
//               setState(() {
//                 isConfirmPasswordVisible = !isConfirmPasswordVisible;
//               });
//             },
//           ),
//         ),
//         validator: (value) =>
//             value != passwordController.text ? "Passwords do not match" : null,
//       ),
//     ],
//   );
// }

//   Widget _signupButton() {
//     return ElevatedButton(
//       onPressed: validateAndSignup,
//       style: ElevatedButton.styleFrom(
//         shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(30)),
//         padding: const EdgeInsets.symmetric(vertical: 16),
//         backgroundColor: Colors.deepPurple,
//         elevation: 5,
//       ),
//       child: const Text(
//         "Sign Up",
//         style: TextStyle(fontSize: 20,color: Colors.black, fontWeight: FontWeight.bold),

//       ),
//     );
//   }

//   Widget _alreadyHaveAccount() {
//     return Row(
//       mainAxisAlignment: MainAxisAlignment.center,
//       children: [
//         const Text(
//           "Already have an account? ",
//           style: TextStyle(color: Colors.black54),
//         ),
//         TextButton(
//           onPressed: () {
//             Navigator.pushReplacement(
//               context,
//               MaterialPageRoute(builder: (context) => const LoginPage()),
//             );
//           },
//           child: const Text(
//             "Login",
//             style: TextStyle(color: Colors.purple, fontWeight: FontWeight.bold),
//           ),
//         ),
//       ],
//     );
//   }
// }

import 'package:flutter/material.dart';
import 'package:myapp/login_page.dart';
import 'package:myapp/therapist_screen.dart';
import 'api_service.dart';

class SignupPage extends StatefulWidget {
  const SignupPage({super.key});

  @override
  _SignupPageState createState() => _SignupPageState();
}

class _SignupPageState extends State<SignupPage> {
  final _formKey = GlobalKey<FormState>();
  final passwordController = TextEditingController();
  final passwordConfirmController = TextEditingController();
  final usernameController = TextEditingController();
  final emailController = TextEditingController();

  String therapistType = "Therapist";
  String userType = "User";

  bool isPasswordVisible = false;
  bool isConfirmPasswordVisible = false;

  @override
  void dispose() {
    passwordController.dispose();
    passwordConfirmController.dispose();
    usernameController.dispose();
    emailController.dispose();
    super.dispose();
  }

  void validateAndSignup() async {
    if (!_formKey.currentState!.validate()) {
      print("Form validation failed");
      return;
    }

    final username = usernameController.text.trim();
    final email = emailController.text.trim();
    final password = passwordController.text.trim();

    print(
        "Attempting to sign up with: username=$username, email=$email, password=$password");

    try {
      // Convert userType to lowercase for API compatibility
      final roleForApi = userType.toLowerCase();
      final result =
          await ApiService.signup(username, email, password, roleForApi);
      print("API Response: $result");

      if (result['success']) {
        print("Signup successful, navigating to LoginPage");
        // ignore: use_build_context_synchronously
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Signup successful!')),
        );
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => const LoginPage()),
        );
      } else {
        // Display the error message from the API response
        showAlert("SignUp Successful",
            result['message'] ?? "Click on the login button");
      }
    } catch (e) {
      print("Error during signup: $e");
      showAlert("Signup Error", "An error occurred. Please try again.");
    }
  }

  void showAlert(String title, String message) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text("OK"),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF8B5CF6),
              Color(0xFFA855F7),
              Color(0xFFC084FC),
            ],
            stops: [0.0, 0.5, 1.0],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  SizedBox(height: MediaQuery.of(context).size.height * 0.02),
                  _header(),
                  const SizedBox(height: 24),
                  _inputFields(),
                  const SizedBox(height: 20),
                  _roleSelector(),
                  const SizedBox(height: 24),
                  _signupButton(),
                  const SizedBox(height: 16),
                  _alreadyHaveAccount(),
                  SizedBox(height: MediaQuery.of(context).size.height * 0.02),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _header() {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 1000),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 50 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: const Icon(
                    Icons.person_add,
                    size: 60,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 24),
                const Text(
                  "Create Your Account",
                  style: TextStyle(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    letterSpacing: 1.2,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  "Join our community and start your mental wellness journey",
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white.withOpacity(0.9),
                    fontWeight: FontWeight.w400,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _inputFields() {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 800),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 30 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.95),
                borderRadius: BorderRadius.circular(24),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  TextFormField(
                    controller: usernameController,
                    decoration: InputDecoration(
                      hintText: "Username",
                      prefixIcon: const Icon(
                        Icons.person_outline,
                        color: Color(0xFF8B5CF6),
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(16),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: const Color(0xFFF8FAFC),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 16,
                      ),
                    ),
                    validator: (value) => value == null || value.length < 3
                        ? "Username must be at least 3 characters"
                        : null,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: emailController,
                    decoration: InputDecoration(
                      hintText: "Email Address",
                      prefixIcon: const Icon(
                        Icons.email_outlined,
                        color: Color(0xFF8B5CF6),
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(16),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: const Color(0xFFF8FAFC),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 16,
                      ),
                    ),
                    validator: (value) {
                      final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+');
                      return value == null || !emailRegex.hasMatch(value)
                          ? "Enter a valid email address"
                          : null;
                    },
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: passwordController,
                    obscureText: !isPasswordVisible,
                    decoration: InputDecoration(
                      hintText: "Password",
                      prefixIcon: const Icon(
                        Icons.lock_outline,
                        color: Color(0xFF8B5CF6),
                      ),
                      suffixIcon: IconButton(
                        icon: Icon(
                          isPasswordVisible
                              ? Icons.visibility_outlined
                              : Icons.visibility_off_outlined,
                          color: const Color(0xFF8B5CF6),
                        ),
                        onPressed: () {
                          setState(() {
                            isPasswordVisible = !isPasswordVisible;
                          });
                        },
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(16),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: const Color(0xFFF8FAFC),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 16,
                      ),
                    ),
                    validator: (value) => value == null || value.length < 6
                        ? "Password must be at least 6 characters long"
                        : null,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: passwordConfirmController,
                    obscureText: !isConfirmPasswordVisible,
                    decoration: InputDecoration(
                      hintText: "Confirm Password",
                      prefixIcon: const Icon(
                        Icons.lock_outline,
                        color: Color(0xFF8B5CF6),
                      ),
                      suffixIcon: IconButton(
                        icon: Icon(
                          isConfirmPasswordVisible
                              ? Icons.visibility_outlined
                              : Icons.visibility_off_outlined,
                          color: const Color(0xFF8B5CF6),
                        ),
                        onPressed: () {
                          setState(() {
                            isConfirmPasswordVisible =
                                !isConfirmPasswordVisible;
                          });
                        },
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(16),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: const Color(0xFFF8FAFC),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 16,
                      ),
                    ),
                    validator: (value) => value != passwordController.text
                        ? "Passwords do not match"
                        : null,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _roleSelector() {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 700),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 20 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.9),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    "I want to join as a:",
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                      color: Color(0xFF1F2937),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: GestureDetector(
                          onTap: () {
                            setState(() {
                              userType = "User";
                            });
                          },
                          child: Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: userType == "User"
                                  ? const Color(0xFF8B5CF6).withOpacity(0.1)
                                  : Colors.grey.shade50,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: userType == "User"
                                    ? const Color(0xFF8B5CF6)
                                    : Colors.grey.shade300,
                                width: 2,
                              ),
                            ),
                            child: Column(
                              children: [
                                Icon(
                                  Icons.person_outline,
                                  color: userType == "User"
                                      ? const Color(0xFF8B5CF6)
                                      : Colors.grey.shade600,
                                  size: 32,
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  "User",
                                  style: TextStyle(
                                    fontWeight: FontWeight.w600,
                                    color: userType == "User"
                                        ? const Color(0xFF8B5CF6)
                                        : Colors.grey.shade700,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  "Seeking support",
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: GestureDetector(
                          onTap: () {
                            setState(() {
                              userType = "Therapist";
                            });
                          },
                          child: Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: userType == "Therapist"
                                  ? const Color(0xFF8B5CF6).withOpacity(0.1)
                                  : Colors.grey.shade50,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: userType == "Therapist"
                                    ? const Color(0xFF8B5CF6)
                                    : Colors.grey.shade300,
                                width: 2,
                              ),
                            ),
                            child: Column(
                              children: [
                                Icon(
                                  Icons.medical_services_outlined,
                                  color: userType == "Therapist"
                                      ? const Color(0xFF8B5CF6)
                                      : Colors.grey.shade600,
                                  size: 32,
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  "Therapist",
                                  style: TextStyle(
                                    fontWeight: FontWeight.w600,
                                    color: userType == "Therapist"
                                        ? const Color(0xFF8B5CF6)
                                        : Colors.grey.shade700,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  "Providing support",
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _signupButton() {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 600),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.scale(
          scale: 0.8 + (0.2 * value),
          child: Opacity(
            opacity: value,
            child: Container(
              width: double.infinity,
              height: 56,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [
                    Color(0xFF8B5CF6),
                    Color(0xFFA855F7),
                  ],
                ),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF8B5CF6).withOpacity(0.4),
                    blurRadius: 15,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: ElevatedButton(
                onPressed: validateAndSignup,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.transparent,
                  shadowColor: Colors.transparent,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
                child: const Text(
                  "Create Account",
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                    letterSpacing: 0.5,
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _alreadyHaveAccount() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          "Already have an account? ",
          style: TextStyle(
            color: Colors.white.withOpacity(0.8),
            fontSize: 14,
          ),
        ),
        TextButton(
          onPressed: () {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(builder: (context) => const LoginPage()),
            );
          },
          child: const Text(
            "Sign In",
            style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
        ),
      ],
    );
  }
}
