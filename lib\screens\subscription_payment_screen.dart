import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;

class SubscriptionPaymentScreen extends StatefulWidget {
  final String userId;

  const SubscriptionPaymentScreen({super.key, required this.userId});

  @override
  _SubscriptionPaymentScreenState createState() =>
      _SubscriptionPaymentScreenState();
}

class _SubscriptionPaymentScreenState extends State<SubscriptionPaymentScreen> {
  List<Map<String, dynamic>> paymentHistory = [];
  List<Map<String, dynamic>> subscriptionPlans = [];
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  Future<void> _initializeData() async {
    await _fetchPaymentHistory();
    _loadSubscriptionPlans();
  }

  Future<void> _fetchPaymentHistory() async {
    try {
      final response = await http.get(
        Uri.parse('http://localhost:3000/api/payments/${widget.userId}'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        setState(() {
          paymentHistory =
              List<Map<String, dynamic>>.from(data['payments'] ?? []);
        });
      }
    } catch (e) {
      print('Error fetching payment history: $e');
    }
  }

  void _loadSubscriptionPlans() {
    setState(() {
      subscriptionPlans = [
        {
          'id': 'basic',
          'name': 'Basic Plan',
          'price': 29.99,
          'duration': 'month',
          'features': [
            'Mood tracking',
            'Basic journaling',
            'Weekly reports',
            'Email support'
          ],
          'color': Colors.blue,
          'popular': false,
        },
        {
          'id': 'premium',
          'name': 'Premium Plan',
          'price': 49.99,
          'duration': 'month',
          'features': [
            'All Basic features',
            'Video consultations (2/month)',
            'AI-powered insights',
            'Priority support',
            'Custom reports'
          ],
          'color': Colors.purple,
          'popular': true,
        },
        {
          'id': 'professional',
          'name': 'Professional Plan',
          'price': 99.99,
          'duration': 'month',
          'features': [
            'All Premium features',
            'Unlimited video consultations',
            'Personal therapist assignment',
            '24/7 crisis support',
            'Family account sharing'
          ],
          'color': Colors.orange,
          'popular': false,
        },
      ];
    });
  }

  Future<void> _processPayment(Map<String, dynamic> plan) async {
    setState(() {
      isLoading = true;
    });

    try {
      // Create payment intent
      final response = await http.post(
        Uri.parse('http://localhost:3000/api/create-payment-intent'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'userId': widget.userId,
          'planId': plan['id'],
          'amount': (plan['price'] * 100).toInt(), // Convert to cents
          'currency': 'usd',
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        // Simulate payment processing
        await Future.delayed(const Duration(seconds: 2));

        // Record successful payment
        await _recordPayment(
            plan,
            data['paymentIntentId'] ??
                'demo_payment_${DateTime.now().millisecondsSinceEpoch}');

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Successfully subscribed to ${plan['name']}!'),
            backgroundColor: Colors.green,
          ),
        );

        _fetchPaymentHistory();
      } else {
        throw Exception('Failed to create payment intent');
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Payment failed: $e')),
      );
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  Future<void> _recordPayment(
      Map<String, dynamic> plan, String paymentIntentId) async {
    try {
      await http.post(
        Uri.parse('http://localhost:3000/api/payments'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'userId': widget.userId,
          'planId': plan['id'],
          'planName': plan['name'],
          'amount': plan['price'],
          'currency': 'USD',
          'paymentIntentId': paymentIntentId,
          'status': 'completed',
          'date': DateTime.now().toIso8601String(),
        }),
      );
    } catch (e) {
      print('Error recording payment: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Subscription & Billing'),
        backgroundColor: Colors.orange,
      ),
      body: DefaultTabController(
        length: 2,
        child: Column(
          children: [
            const TabBar(
              labelColor: Colors.orange,
              tabs: [
                Tab(text: 'Plans', icon: Icon(Icons.credit_card)),
                Tab(text: 'History', icon: Icon(Icons.history)),
              ],
            ),
            Expanded(
              child: TabBarView(
                children: [
                  _buildSubscriptionTab(),
                  _buildHistoryTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubscriptionTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Choose Your Plan',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'Select the plan that best fits your mental health journey',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
          ),
          const SizedBox(height: 20),
          ...subscriptionPlans.map((plan) => _buildPlanCard(plan)).toList(),
        ],
      ),
    );
  }

  Widget _buildPlanCard(Map<String, dynamic> plan) {
    final isPopular = plan['popular'] as bool;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Card(
        elevation: isPopular ? 8 : 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(
            color: isPopular ? plan['color'] : Colors.transparent,
            width: 2,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    plan['name'],
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: plan['color'],
                        ),
                  ),
                  if (isPopular)
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: plan['color'],
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Text(
                        'POPULAR',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '\$${plan['price']}',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  Text(
                    '/${plan['duration']}',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              ...plan['features']
                  .map<Widget>(
                    (feature) => Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Row(
                        children: [
                          Icon(
                            Icons.check_circle,
                            color: plan['color'],
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Expanded(child: Text(feature)),
                        ],
                      ),
                    ),
                  )
                  .toList(),
              const SizedBox(height: 20),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: isLoading ? null : () => _showPaymentDialog(plan),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: plan['color'],
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Text(
                    'Subscribe Now',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showPaymentDialog(Map<String, dynamic> plan) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Subscription'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Plan: ${plan['name']}'),
            Text('Price: \$${plan['price']}/${plan['duration']}'),
            const SizedBox(height: 16),
            const Text(
              'This is a demo payment. In a real app, you would integrate with Stripe\'s payment UI.',
              style: TextStyle(fontStyle: FontStyle.italic),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _processPayment(plan);
            },
            child: const Text('Confirm Payment'),
          ),
        ],
      ),
    );
  }

  Widget _buildHistoryTab() {
    if (paymentHistory.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.payment, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No payment history',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            SizedBox(height: 8),
            Text(
              'Your payment history will appear here',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: paymentHistory.length,
      itemBuilder: (context, index) {
        final payment = paymentHistory[index];
        final date = DateTime.parse(payment['date']);

        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: Colors.green.withOpacity(0.2),
              child: const Icon(Icons.check, color: Colors.green),
            ),
            title: Text(payment['planName']),
            subtitle: Text(
              '${date.day}/${date.month}/${date.year} - \$${payment['amount']} ${payment['currency']}',
            ),
            trailing: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                payment['status'].toUpperCase(),
                style: const TextStyle(
                  color: Colors.green,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
