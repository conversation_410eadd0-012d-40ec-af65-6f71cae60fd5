import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;

class PaymentService {
  static const String baseUrl = 'http://localhost:3000/api';

  // Initialize payment service (simplified for demo)
  static Future<void> initializeStripe() async {
    // Mock initialization for demo purposes
    await Future.delayed(const Duration(milliseconds: 100));
  }

  // Create payment intent
  static Future<Map<String, dynamic>?> createPaymentIntent({
    required double amount,
    required String currency,
    required String userId,
    required String appointmentId,
    String? description,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/payments/create-intent'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'amount': (amount * 100).round(), // Convert to cents
          'currency': currency,
          'userId': userId,
          'appointmentId': appointmentId,
          'description': description,
        }),
      );

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception('Failed to create payment intent');
      }
    } catch (e) {
      print('Error creating payment intent: $e');
      return null;
    }
  }

  // Process payment (simplified demo version)
  static Future<bool> processPayment({
    required String clientSecret,
    required BuildContext context,
    String? paymentMethodId,
  }) async {
    try {
      // Simulate payment processing
      await Future.delayed(const Duration(seconds: 2));

      // Mock successful payment for demo
      return true;
    } catch (e) {
      debugPrint('Payment error: $e');
      _showErrorDialog(context, 'An unexpected error occurred');
      return false;
    }
  }

  // Confirm payment
  static Future<bool> confirmPayment({
    required String paymentIntentId,
    required String userId,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/payments/confirm'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'paymentIntentId': paymentIntentId,
          'userId': userId,
        }),
      );

      return response.statusCode == 200;
    } catch (e) {
      print('Error confirming payment: $e');
      return false;
    }
  }

  // Get payment history
  static Future<List<Map<String, dynamic>>> getPaymentHistory(
      String userId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/payments/history/$userId'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        List<dynamic> data = json.decode(response.body);
        return data.cast<Map<String, dynamic>>();
      } else {
        throw Exception('Failed to load payment history');
      }
    } catch (e) {
      print('Error getting payment history: $e');
      return [];
    }
  }

  // Get appointment pricing
  static Future<Map<String, dynamic>?> getAppointmentPricing({
    required String therapistId,
    required String appointmentType,
    required int duration,
  }) async {
    try {
      final response = await http.get(
        Uri.parse(
            '$baseUrl/payments/pricing?therapistId=$therapistId&type=$appointmentType&duration=$duration'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception('Failed to get pricing');
      }
    } catch (e) {
      print('Error getting pricing: $e');
      return null;
    }
  }

  // Process refund
  static Future<bool> processRefund({
    required String paymentIntentId,
    required String userId,
    double? amount,
    String? reason,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/payments/refund'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'paymentIntentId': paymentIntentId,
          'userId': userId,
          'amount': amount != null ? (amount * 100).round() : null,
          'reason': reason,
        }),
      );

      return response.statusCode == 200;
    } catch (e) {
      print('Error processing refund: $e');
      return false;
    }
  }

  // Save payment method
  static Future<bool> savePaymentMethod({
    required String userId,
    required String paymentMethodId,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/payments/save-method'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'userId': userId,
          'paymentMethodId': paymentMethodId,
        }),
      );

      return response.statusCode == 200;
    } catch (e) {
      print('Error saving payment method: $e');
      return false;
    }
  }

  // Get saved payment methods
  static Future<List<Map<String, dynamic>>> getSavedPaymentMethods(
      String userId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/payments/methods/$userId'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        List<dynamic> data = json.decode(response.body);
        return data.cast<Map<String, dynamic>>();
      } else {
        throw Exception('Failed to load payment methods');
      }
    } catch (e) {
      print('Error getting payment methods: $e');
      return [];
    }
  }

  // Delete payment method
  static Future<bool> deletePaymentMethod({
    required String userId,
    required String paymentMethodId,
  }) async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl/payments/methods/$paymentMethodId'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({'userId': userId}),
      );

      return response.statusCode == 200;
    } catch (e) {
      print('Error deleting payment method: $e');
      return false;
    }
  }

  // Calculate total cost with taxes and fees
  static Map<String, double> calculateTotalCost({
    required double baseAmount,
    double taxRate = 0.08, // 8% tax
    double serviceFee = 2.50,
  }) {
    double subtotal = baseAmount;
    double tax = subtotal * taxRate;
    double total = subtotal + tax + serviceFee;

    return {
      'subtotal': subtotal,
      'tax': tax,
      'serviceFee': serviceFee,
      'total': total,
    };
  }

  // Validate payment amount
  static bool isValidAmount(double amount) {
    return amount > 0 && amount <= 10000; // Max $10,000
  }

  // Format currency
  static String formatCurrency(double amount, {String currency = 'USD'}) {
    return '\$${amount.toStringAsFixed(2)}';
  }

  // Get payment status
  static Future<String?> getPaymentStatus(String paymentIntentId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/payments/status/$paymentIntentId'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        Map<String, dynamic> data = json.decode(response.body);
        return data['status'];
      }
      return null;
    } catch (e) {
      print('Error getting payment status: $e');
      return null;
    }
  }

  // Show error dialog
  static void _showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Payment Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  // Show success dialog
  static void showSuccessDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Payment Successful'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  // Create subscription
  static Future<Map<String, dynamic>?> createSubscription({
    required String userId,
    required String priceId,
    required String paymentMethodId,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/payments/subscription'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'userId': userId,
          'priceId': priceId,
          'paymentMethodId': paymentMethodId,
        }),
      );

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception('Failed to create subscription');
      }
    } catch (e) {
      print('Error creating subscription: $e');
      return null;
    }
  }

  // Cancel subscription
  static Future<bool> cancelSubscription({
    required String subscriptionId,
    required String userId,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/payments/subscription/cancel'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'subscriptionId': subscriptionId,
          'userId': userId,
        }),
      );

      return response.statusCode == 200;
    } catch (e) {
      print('Error canceling subscription: $e');
      return false;
    }
  }
}
