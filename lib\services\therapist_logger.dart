import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:intl/intl.dart';

enum LogLevel {
  debug,
  info,
  warning,
  error,
  critical
}

enum LogCategory {
  authentication,
  dataAccess,
  userInteraction,
  apiCall,
  systemEvent,
  security,
  performance
}

class LogEntry {
  final String id;
  final DateTime timestamp;
  final LogLevel level;
  final LogCategory category;
  final String message;
  final String? therapistId;
  final Map<String, dynamic>? metadata;
  final String? stackTrace;

  LogEntry({
    required this.id,
    required this.timestamp,
    required this.level,
    required this.category,
    required this.message,
    this.therapistId,
    this.metadata,
    this.stackTrace,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'timestamp': timestamp.toIso8601String(),
      'level': level.name,
      'category': category.name,
      'message': message,
      'therapistId': therapistId,
      'metadata': metadata,
      'stackTrace': stackTrace,
    };
  }

  factory LogEntry.fromJson(Map<String, dynamic> json) {
    return LogEntry(
      id: json['id'],
      timestamp: DateTime.parse(json['timestamp']),
      level: LogLevel.values.firstWhere((e) => e.name == json['level']),
      category: LogCategory.values.firstWhere((e) => e.name == json['category']),
      message: json['message'],
      therapistId: json['therapistId'],
      metadata: json['metadata'],
      stackTrace: json['stackTrace'],
    );
  }
}

class TherapistLogger {
  static final TherapistLogger _instance = TherapistLogger._internal();
  factory TherapistLogger() => _instance;
  TherapistLogger._internal();

  final List<LogEntry> _logs = [];
  final int _maxLogs = 1000;
  String? _currentTherapistId;

  // Initialize logger with therapist ID
  void initialize(String therapistId) {
    _currentTherapistId = therapistId;
    _loadStoredLogs();
    logInfo(
      category: LogCategory.systemEvent,
      message: 'Logger initialized for therapist',
      metadata: {'therapistId': therapistId},
    );
  }

  // Core logging method
  void _log({
    required LogLevel level,
    required LogCategory category,
    required String message,
    Map<String, dynamic>? metadata,
    String? stackTrace,
  }) {
    final entry = LogEntry(
      id: _generateId(),
      timestamp: DateTime.now(),
      level: level,
      category: category,
      message: message,
      therapistId: _currentTherapistId,
      metadata: metadata,
      stackTrace: stackTrace,
    );

    _logs.add(entry);
    
    // Keep only the most recent logs
    if (_logs.length > _maxLogs) {
      _logs.removeAt(0);
    }

    // Print to console in debug mode
    if (kDebugMode) {
      _printLog(entry);
    }

    // Store logs persistently
    _storeLogs();
  }

  // Convenience methods for different log levels
  void logDebug({
    required LogCategory category,
    required String message,
    Map<String, dynamic>? metadata,
  }) {
    _log(
      level: LogLevel.debug,
      category: category,
      message: message,
      metadata: metadata,
    );
  }

  void logInfo({
    required LogCategory category,
    required String message,
    Map<String, dynamic>? metadata,
  }) {
    _log(
      level: LogLevel.info,
      category: category,
      message: message,
      metadata: metadata,
    );
  }

  void logWarning({
    required LogCategory category,
    required String message,
    Map<String, dynamic>? metadata,
  }) {
    _log(
      level: LogLevel.warning,
      category: category,
      message: message,
      metadata: metadata,
    );
  }

  void logError({
    required LogCategory category,
    required String message,
    Map<String, dynamic>? metadata,
    String? stackTrace,
  }) {
    _log(
      level: LogLevel.error,
      category: category,
      message: message,
      metadata: metadata,
      stackTrace: stackTrace,
    );
  }

  void logCritical({
    required LogCategory category,
    required String message,
    Map<String, dynamic>? metadata,
    String? stackTrace,
  }) {
    _log(
      level: LogLevel.critical,
      category: category,
      message: message,
      metadata: metadata,
      stackTrace: stackTrace,
    );
  }

  // Specialized logging methods for common therapist activities
  void logLogin(String therapistId, {bool success = true}) {
    logInfo(
      category: LogCategory.authentication,
      message: success ? 'Therapist login successful' : 'Therapist login failed',
      metadata: {
        'therapistId': therapistId,
        'success': success,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  void logLogout(String therapistId) {
    logInfo(
      category: LogCategory.authentication,
      message: 'Therapist logout',
      metadata: {
        'therapistId': therapistId,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  void logApiCall({
    required String endpoint,
    required String method,
    required int statusCode,
    required Duration duration,
    Map<String, dynamic>? requestData,
    Map<String, dynamic>? responseData,
  }) {
    logInfo(
      category: LogCategory.apiCall,
      message: 'API call: $method $endpoint',
      metadata: {
        'endpoint': endpoint,
        'method': method,
        'statusCode': statusCode,
        'duration': duration.inMilliseconds,
        'requestData': requestData,
        'responseData': responseData,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  void logPatientAccess(String patientId, String action) {
    logInfo(
      category: LogCategory.dataAccess,
      message: 'Patient data access: $action',
      metadata: {
        'patientId': patientId,
        'action': action,
        'therapistId': _currentTherapistId,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  void logAppointmentAction(String appointmentId, String action) {
    logInfo(
      category: LogCategory.userInteraction,
      message: 'Appointment action: $action',
      metadata: {
        'appointmentId': appointmentId,
        'action': action,
        'therapistId': _currentTherapistId,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  void logSecurityEvent(String event, {Map<String, dynamic>? details}) {
    logWarning(
      category: LogCategory.security,
      message: 'Security event: $event',
      metadata: {
        'event': event,
        'details': details,
        'therapistId': _currentTherapistId,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  void logPerformance(String operation, Duration duration) {
    logInfo(
      category: LogCategory.performance,
      message: 'Performance: $operation',
      metadata: {
        'operation': operation,
        'duration': duration.inMilliseconds,
        'therapistId': _currentTherapistId,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  // Utility methods
  String _generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  void _printLog(LogEntry entry) {
    final timestamp = DateFormat('HH:mm:ss.SSS').format(entry.timestamp);
    final level = entry.level.name.toUpperCase().padRight(8);
    final category = entry.category.name.toUpperCase().padRight(12);
    
    print('[$timestamp] $level [$category] ${entry.message}');
    if (entry.metadata != null) {
      print('  Metadata: ${jsonEncode(entry.metadata)}');
    }
    if (entry.stackTrace != null) {
      print('  Stack: ${entry.stackTrace}');
    }
  }

  Future<void> _storeLogs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final logsJson = _logs.map((log) => log.toJson()).toList();
      await prefs.setString('therapist_logs_${_currentTherapistId}', jsonEncode(logsJson));
    } catch (e) {
      if (kDebugMode) {
        print('Failed to store logs: $e');
      }
    }
  }

  Future<void> _loadStoredLogs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final logsString = prefs.getString('therapist_logs_${_currentTherapistId}');
      if (logsString != null) {
        final logsJson = jsonDecode(logsString) as List;
        _logs.clear();
        _logs.addAll(logsJson.map((json) => LogEntry.fromJson(json)));
      }
    } catch (e) {
      if (kDebugMode) {
        print('Failed to load stored logs: $e');
      }
    }
  }

  // Get logs with filtering
  List<LogEntry> getLogs({
    LogLevel? level,
    LogCategory? category,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  }) {
    var filteredLogs = List<LogEntry>.from(_logs);

    if (level != null) {
      filteredLogs = filteredLogs.where((log) => log.level == level).toList();
    }

    if (category != null) {
      filteredLogs = filteredLogs.where((log) => log.category == category).toList();
    }

    if (startDate != null) {
      filteredLogs = filteredLogs.where((log) => log.timestamp.isAfter(startDate)).toList();
    }

    if (endDate != null) {
      filteredLogs = filteredLogs.where((log) => log.timestamp.isBefore(endDate)).toList();
    }

    // Sort by timestamp (newest first)
    filteredLogs.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    if (limit != null && limit > 0) {
      filteredLogs = filteredLogs.take(limit).toList();
    }

    return filteredLogs;
  }

  // Export logs
  String exportLogs({
    LogLevel? level,
    LogCategory? category,
    DateTime? startDate,
    DateTime? endDate,
  }) {
    final logs = getLogs(
      level: level,
      category: category,
      startDate: startDate,
      endDate: endDate,
    );

    final buffer = StringBuffer();
    buffer.writeln('Therapist Activity Log Export');
    buffer.writeln('Generated: ${DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now())}');
    buffer.writeln('Therapist ID: $_currentTherapistId');
    buffer.writeln('Total Entries: ${logs.length}');
    buffer.writeln('${'=' * 80}');

    for (final log in logs) {
      buffer.writeln();
      buffer.writeln('Timestamp: ${DateFormat('yyyy-MM-dd HH:mm:ss.SSS').format(log.timestamp)}');
      buffer.writeln('Level: ${log.level.name.toUpperCase()}');
      buffer.writeln('Category: ${log.category.name.toUpperCase()}');
      buffer.writeln('Message: ${log.message}');
      
      if (log.metadata != null) {
        buffer.writeln('Metadata: ${jsonEncode(log.metadata)}');
      }
      
      if (log.stackTrace != null) {
        buffer.writeln('Stack Trace: ${log.stackTrace}');
      }
      
      buffer.writeln('-' * 40);
    }

    return buffer.toString();
  }

  // Clear logs
  Future<void> clearLogs() async {
    _logs.clear();
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('therapist_logs_${_currentTherapistId}');
    
    logInfo(
      category: LogCategory.systemEvent,
      message: 'Logs cleared',
      metadata: {'clearedAt': DateTime.now().toIso8601String()},
    );
  }

  // Get log statistics
  Map<String, dynamic> getLogStatistics() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final thisWeek = now.subtract(Duration(days: now.weekday - 1));
    
    return {
      'totalLogs': _logs.length,
      'todayLogs': _logs.where((log) => log.timestamp.isAfter(today)).length,
      'thisWeekLogs': _logs.where((log) => log.timestamp.isAfter(thisWeek)).length,
      'errorLogs': _logs.where((log) => log.level == LogLevel.error || log.level == LogLevel.critical).length,
      'categories': LogCategory.values.map((cat) => {
        'category': cat.name,
        'count': _logs.where((log) => log.category == cat).length,
      }).toList(),
      'levels': LogLevel.values.map((level) => {
        'level': level.name,
        'count': _logs.where((log) => log.level == level).length,
      }).toList(),
    };
  }
}
