# 📧 Gmail Setup Guide for MindEase Forgot Password

## 🔐 Setting up Gmail App Password

To enable the forgot password email functionality, you need to set up a Gmail App Password for `<EMAIL>`.

### Step 1: Enable 2-Factor Authentication
1. Go to [Google Account Settings](https://myaccount.google.com/)
2. Click on "Security" in the left sidebar
3. Under "Signing in to Google", click on "2-Step Verification"
4. Follow the setup process to enable 2FA if not already enabled

### Step 2: Generate App Password
1. After 2FA is enabled, go back to "Security"
2. Under "Signing in to Google", click on "App passwords"
3. Select "Mail" as the app
4. Select "Other (Custom name)" as the device
5. Enter "MindEase App" as the custom name
6. Click "Generate"
7. **Copy the 16-character password** (it will look like: `abcd efgh ijkl mnop`)

### Step 3: Update Environment Variables
1. Open the `.env` file in the project folder
2. Replace `your_gmail_app_password_here` with the generated app password
3. Save the file

```env
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=abcd efgh ijkl mnop  # Replace with your actual app password
EMAIL_FROM=<EMAIL>
```

### Step 4: Test the Setup
1. Restart the backend server: `npm run dev`
2. Test the forgot password functionality from the Flutter app
3. Check the email inbox for the password reset email

## 🎨 Email Template Features

The enhanced forgot password system includes:

- ✨ **Beautiful HTML email template** with MindEase branding
- 🔐 **Secure random password generation** (12 characters)
- 📱 **Responsive email design** that works on all devices
- ⚠️ **Security warnings** and instructions
- 🎯 **Professional styling** with purple gradient theme
- 📧 **Both HTML and plain text** versions

## 🔧 Technical Details

- **SMTP Server**: Gmail (smtp.gmail.com:587)
- **Authentication**: OAuth2 with App Password
- **Security**: TLS encryption
- **From Address**: <EMAIL>
- **Subject**: 🔐 MindEase - Password Reset Request

## 🚨 Security Notes

- App passwords are more secure than using your main Gmail password
- The generated password is temporary and should be changed after login
- All email communications are encrypted in transit
- Failed authentication attempts are logged for security monitoring

## 📞 Support

If you encounter any issues:
1. Verify 2FA is enabled on the Gmail account
2. Ensure the app password is correctly copied (no spaces)
3. Check the server logs for specific error messages
4. Restart the backend server after updating .env file

---

**Ready to test!** 🚀 Once you've set up the app password, the forgot password feature will work seamlessly!
