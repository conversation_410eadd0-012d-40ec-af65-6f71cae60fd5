// appointmentRoutes.js
const express = require('express');
const router = express.Router();
const AppointmentController = require('../controllers/appointmentController');

// Get all appointments for a user
router.get('/user/:userId', AppointmentController.getUserAppointments);

// Get all appointments for a therapist
router.get('/therapist/:therapistId', AppointmentController.getTherapistAppointments);

// Get specific appointment details
router.get('/:appointmentId', AppointmentController.getAppointmentById);

// Book a new appointment
router.post('/', AppointmentController.bookAppointment);

// Update appointment details
router.put('/:appointmentId', AppointmentController.updateAppointment);

// Cancel an appointment
router.put('/:appointmentId/cancel', AppointmentController.cancelAppointment);

// Get available time slots for a therapist on a specific day
router.get('/available/:therapistId/:date', AppointmentController.getAvailableTimeSlots);

module.exports = router;