import 'dart:convert';
import 'package:http/http.dart' as http;
import 'dart:math';

// Simple position class for demo
class SimplePosition {
  final double latitude;
  final double longitude;
  final DateTime timestamp;

  SimplePosition({
    required this.latitude,
    required this.longitude,
    required this.timestamp,
  });
}

class LocationService {
  static const String baseUrl = 'http://localhost:3000/api';

  // Mock current location for demo (New York City)
  static Future<SimplePosition?> getCurrentLocation() async {
    try {
      // Simulate getting location
      await Future.delayed(const Duration(seconds: 1));
      
      // Return mock location (NYC coordinates)
      return SimplePosition(
        latitude: 40.7128,
        longitude: -74.0060,
        timestamp: DateTime.now(),
      );
    } catch (e) {
      print('Error getting location: $e');
      return null;
    }
  }

  // Mock address from coordinates
  static Future<String> getAddressFromCoordinates(double latitude, double longitude) async {
    try {
      // Mock address lookup
      await Future.delayed(const Duration(milliseconds: 500));
      return 'New York, NY, USA';
    } catch (e) {
      print('Error getting address: $e');
      return 'Unknown location';
    }
  }

  // Mock coordinates from address
  static Future<SimplePosition?> getCoordinatesFromAddress(String address) async {
    try {
      // Mock geocoding
      await Future.delayed(const Duration(milliseconds: 500));
      return SimplePosition(
        latitude: 40.7128,
        longitude: -74.0060,
        timestamp: DateTime.now(),
      );
    } catch (e) {
      print('Error getting coordinates: $e');
      return null;
    }
  }

  // Find nearby therapists
  static Future<List<Map<String, dynamic>>> findNearbyTherapists({
    required double latitude,
    required double longitude,
    double radiusKm = 10.0,
  }) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/location/therapists/nearby?lat=$latitude&lng=$longitude&radius=$radiusKm'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        List<dynamic> data = json.decode(response.body);
        return data.cast<Map<String, dynamic>>();
      } else {
        // Return mock data if API fails
        return _getMockTherapists();
      }
    } catch (e) {
      print('Error finding nearby therapists: $e');
      // Return mock data for demo
      return _getMockTherapists();
    }
  }

  // Mock therapists data
  static List<Map<String, dynamic>> _getMockTherapists() {
    return [
      {
        'id': '1',
        'name': 'Dr. Sarah Johnson',
        'specialty': 'Anxiety & Depression',
        'rating': 4.8,
        'reviewCount': 127,
        'distance': 2.3,
        'location': {
          'latitude': 40.7589,
          'longitude': -73.9851,
        },
        'profileImage': null,
        'phone': '+****************',
        'email': '<EMAIL>',
        'description': 'Experienced therapist specializing in cognitive behavioral therapy for anxiety and depression.',
      },
      {
        'id': '2',
        'name': 'Dr. Michael Chen',
        'specialty': 'Trauma & PTSD',
        'rating': 4.9,
        'reviewCount': 89,
        'distance': 3.7,
        'location': {
          'latitude': 40.7505,
          'longitude': -73.9934,
        },
        'profileImage': null,
        'phone': '+****************',
        'email': '<EMAIL>',
        'description': 'Specialized in trauma-informed therapy and EMDR techniques.',
      },
      {
        'id': '3',
        'name': 'Dr. Emily Rodriguez',
        'specialty': 'Family Therapy',
        'rating': 4.7,
        'reviewCount': 156,
        'distance': 5.1,
        'location': {
          'latitude': 40.7282,
          'longitude': -73.9942,
        },
        'profileImage': null,
        'phone': '+****************',
        'email': '<EMAIL>',
        'description': 'Family and couples therapy with over 15 years of experience.',
      },
    ];
  }

  // Calculate distance between two points using Haversine formula
  static double calculateDistance(
    double lat1, double lon1,
    double lat2, double lon2,
  ) {
    const double earthRadius = 6371; // Earth's radius in kilometers
    
    double dLat = _degreesToRadians(lat2 - lat1);
    double dLon = _degreesToRadians(lon2 - lon1);
    
    double a = sin(dLat / 2) * sin(dLat / 2) +
        cos(_degreesToRadians(lat1)) * cos(_degreesToRadians(lat2)) *
        sin(dLon / 2) * sin(dLon / 2);
    
    double c = 2 * atan2(sqrt(a), sqrt(1 - a));
    
    return earthRadius * c;
  }

  static double _degreesToRadians(double degrees) {
    return degrees * (pi / 180);
  }

  // Save user location
  static Future<bool> saveUserLocation({
    required String userId,
    required double latitude,
    required double longitude,
    String? address,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/location/updateLocation'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'userId': userId,
          'latitude': latitude,
          'longitude': longitude,
          'address': address,
        }),
      );

      return response.statusCode == 200;
    } catch (e) {
      print('Error saving location: $e');
      return false;
    }
  }

  // Get location-based recommendations
  static Future<Map<String, dynamic>> getLocationBasedRecommendations({
    required double latitude,
    required double longitude,
  }) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/location/recommendations/location?lat=$latitude&lng=$longitude'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        // Return mock recommendations
        return _getMockRecommendations();
      }
    } catch (e) {
      print('Error getting recommendations: $e');
      return _getMockRecommendations();
    }
  }

  static Map<String, dynamic> _getMockRecommendations() {
    return {
      'nearbyTherapists': 3,
      'emergencyContacts': [
        {
          'name': 'National Suicide Prevention Lifeline',
          'phone': '988',
          'available': '24/7'
        },
        {
          'name': 'Crisis Text Line',
          'phone': 'Text HOME to 741741',
          'available': '24/7'
        }
      ],
      'localResources': [
        {
          'name': 'Community Mental Health Center',
          'type': 'clinic',
          'distance': '2.5 km'
        },
        {
          'name': 'Support Group Meetings',
          'type': 'support_group',
          'distance': '1.8 km'
        }
      ]
    };
  }

  // Mock permission methods
  static Future<bool> hasLocationPermission() async {
    return true; // Always return true for demo
  }

  static Future<bool> requestLocationPermission() async {
    return true; // Always return true for demo
  }

  static Future<bool> isLocationServiceEnabled() async {
    return true; // Always return true for demo
  }

  static Future<void> openLocationSettings() async {
    // Mock implementation
  }

  // Get distance to therapist
  static Future<double?> getDistanceToTherapist({
    required String therapistId,
    required double userLatitude,
    required double userLongitude,
  }) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/location/therapists/$therapistId/location'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        Map<String, dynamic> data = json.decode(response.body);
        double therapistLat = data['latitude'];
        double therapistLng = data['longitude'];
        
        return calculateDistance(
          userLatitude, userLongitude,
          therapistLat, therapistLng,
        );
      }
      return null;
    } catch (e) {
      print('Error getting distance to therapist: $e');
      return null;
    }
  }

  // Search therapists by location
  static Future<List<Map<String, dynamic>>> searchTherapistsByLocation({
    required String location,
    double radiusKm = 25.0,
  }) async {
    try {
      SimplePosition? coordinates = await getCoordinatesFromAddress(location);
      if (coordinates != null) {
        return await findNearbyTherapists(
          latitude: coordinates.latitude,
          longitude: coordinates.longitude,
          radiusKm: radiusKm,
        );
      }
      return _getMockTherapists();
    } catch (e) {
      print('Error searching therapists by location: $e');
      return _getMockTherapists();
    }
  }
}
