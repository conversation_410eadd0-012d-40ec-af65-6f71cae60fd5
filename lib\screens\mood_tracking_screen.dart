import 'package:flutter/material.dart';
import 'package:myapp/api_service.dart';

class MoodTrackingScreen extends StatefulWidget {
  final String userId;

  const MoodTrackingScreen({super.key, required this.userId});

  @override
  _MoodTrackingScreenState createState() => _MoodTrackingScreenState();
}

class _MoodTrackingScreenState extends State<MoodTrackingScreen> {
  int selectedMood = -1;
  String moodNote = '';
  List<String> selectedActivities = [];
  bool isLoading = false;
  List<Map<String, dynamic>> moodHistory = [];

  final List<Map<String, dynamic>> moods = [
    {'emoji': '😢', 'label': 'Very Sad', 'value': 1, 'color': Colors.red},
    {'emoji': '😔', 'label': 'Sad', 'value': 2, 'color': Colors.orange},
    {'emoji': '😐', 'label': 'Neutral', 'value': 3, 'color': Colors.grey},
    {'emoji': '😊', 'label': 'Happy', 'value': 4, 'color': Colors.lightGreen},
    {'emoji': '😄', 'label': 'Very Happy', 'value': 5, 'color': Colors.green},
  ];

  final List<String> activities = [
    'Exercise',
    'Work',
    'Social',
    'Family',
    'Hobbies',
    'Rest',
    'Study',
    'Travel',
    'Music',
    'Reading',
    'Cooking',
    'Gaming'
  ];

  @override
  void initState() {
    super.initState();
    _fetchMoodHistory();
  }

  Future<void> _fetchMoodHistory() async {
    try {
      final result = await ApiService.getMoodEntries(widget.userId);
      if (result['success'] && mounted) {
        setState(() {
          moodHistory = List<Map<String, dynamic>>.from(
              result['data']['moodEntries'] ?? []);
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading mood history: $e')),
        );
      }
    }
  }

  Future<void> _saveMoodEntry() async {
    if (selectedMood == -1) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select a mood')),
      );
      return;
    }

    setState(() {
      isLoading = true;
    });

    try {
      final moodLabel =
          moods.firstWhere((mood) => mood['value'] == selectedMood)['label'];
      final result = await ApiService.saveMoodEntry(
          widget.userId, moodLabel, moodNote.isEmpty ? null : moodNote);

      if (result['success'] && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Mood entry saved successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        _resetForm();
        _fetchMoodHistory();
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: ${result['error']}')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  void _resetForm() {
    setState(() {
      selectedMood = -1;
      moodNote = '';
      selectedActivities.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Mood Tracking'),
        backgroundColor: Colors.blue,
      ),
      body: DefaultTabController(
        length: 2,
        child: Column(
          children: [
            const TabBar(
              labelColor: Colors.blue,
              tabs: [
                Tab(text: 'Track Mood', icon: Icon(Icons.add)),
                Tab(text: 'History', icon: Icon(Icons.history)),
              ],
            ),
            Expanded(
              child: TabBarView(
                children: [
                  _buildTrackMoodTab(),
                  _buildHistoryTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTrackMoodTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'How are you feeling today?',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 20),

          // Mood selection
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Select your mood:',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: moods.map((mood) {
                      final isSelected = selectedMood == mood['value'];
                      return GestureDetector(
                        onTap: () {
                          setState(() {
                            selectedMood = mood['value'];
                          });
                        },
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: isSelected
                                ? mood['color'].withOpacity(0.2)
                                : null,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: isSelected
                                  ? mood['color']
                                  : Colors.grey.shade300,
                              width: 2,
                            ),
                          ),
                          child: Column(
                            children: [
                              Text(
                                mood['emoji'],
                                style: const TextStyle(fontSize: 32),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                mood['label'],
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: isSelected
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),

          // Activities
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'What activities did you do?',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                  const SizedBox(height: 16),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: activities.map((activity) {
                      final isSelected = selectedActivities.contains(activity);
                      return FilterChip(
                        label: Text(activity),
                        selected: isSelected,
                        onSelected: (selected) {
                          setState(() {
                            if (selected) {
                              selectedActivities.add(activity);
                            } else {
                              selectedActivities.remove(activity);
                            }
                          });
                        },
                        selectedColor: Colors.blue.withOpacity(0.2),
                        checkmarkColor: Colors.blue,
                      );
                    }).toList(),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),

          // Note
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Add a note (optional):',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    maxLines: 4,
                    decoration: const InputDecoration(
                      hintText:
                          'How was your day? What made you feel this way?',
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (value) {
                      setState(() {
                        moodNote = value;
                      });
                    },
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 30),

          // Save button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: isLoading ? null : _saveMoodEntry,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: isLoading
                  ? const CircularProgressIndicator(color: Colors.white)
                  : const Text(
                      'Save Mood Entry',
                      style:
                          TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHistoryTab() {
    if (moodHistory.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.mood, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No mood entries yet',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            SizedBox(height: 8),
            Text(
              'Start tracking your mood to see your history here',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: moodHistory.length,
      itemBuilder: (context, index) {
        final entry = moodHistory[index];
        final mood = moods.firstWhere((m) => m['value'] == entry['mood']);
        final date = DateTime.parse(entry['date']);

        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: mood['color'].withOpacity(0.2),
              child: Text(
                mood['emoji'],
                style: const TextStyle(fontSize: 20),
              ),
            ),
            title: Text(mood['label']),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${date.day}/${date.month}/${date.year} at ${date.hour}:${date.minute.toString().padLeft(2, '0')}',
                ),
                if (entry['note'] != null && entry['note'].isNotEmpty)
                  Text(
                    entry['note'],
                    style: const TextStyle(fontStyle: FontStyle.italic),
                  ),
                if (entry['activities'] != null &&
                    entry['activities'].isNotEmpty)
                  Wrap(
                    spacing: 4,
                    children: (entry['activities'] as List)
                        .map<Widget>(
                          (activity) => Chip(
                            label: Text(activity),
                            materialTapTargetSize:
                                MaterialTapTargetSize.shrinkWrap,
                          ),
                        )
                        .toList(),
                  ),
              ],
            ),
            isThreeLine: true,
          ),
        );
      },
    );
  }
}
