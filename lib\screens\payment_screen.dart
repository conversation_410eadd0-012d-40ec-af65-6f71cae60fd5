import 'package:flutter/material.dart';
import '../services/payment_service.dart';

class PaymentScreen extends StatefulWidget {
  final String appointmentId;
  final String therapistName;
  final String appointmentType;
  final int duration;
  final double baseAmount;
  final String userId;

  const PaymentScreen({
    super.key,
    required this.appointmentId,
    required this.therapistName,
    required this.appointmentType,
    required this.duration,
    required this.baseAmount,
    required this.userId,
  });

  @override
  State<PaymentScreen> createState() => _PaymentScreenState();
}

class _PaymentScreenState extends State<PaymentScreen> {
  bool _isLoading = false;
  Map<String, double> _costBreakdown = {};
  List<Map<String, dynamic>> _savedPaymentMethods = [];
  String? _selectedPaymentMethodId;
  bool _savePaymentMethod = false;

  @override
  void initState() {
    super.initState();
    _initializePayment();
  }

  Future<void> _initializePayment() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Initialize Stripe
      await PaymentService.initializeStripe();

      // Calculate cost breakdown
      _costBreakdown = PaymentService.calculateTotalCost(
        baseAmount: widget.baseAmount,
      );

      // Load saved payment methods
      _savedPaymentMethods =
          await PaymentService.getSavedPaymentMethods(widget.userId);

      setState(() {});
    } catch (e) {
      print('Error initializing payment: $e');
      _showErrorDialog('Failed to initialize payment. Please try again.');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _processPayment() async {
    if (!PaymentService.isValidAmount(_costBreakdown['total'] ?? 0)) {
      _showErrorDialog('Invalid payment amount');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Create payment intent
      Map<String, dynamic>? paymentIntent =
          await PaymentService.createPaymentIntent(
        amount: _costBreakdown['total']!,
        currency: 'usd',
        userId: widget.userId,
        appointmentId: widget.appointmentId,
        description: 'Therapy session with ${widget.therapistName}',
      );

      if (paymentIntent == null) {
        throw Exception('Failed to create payment intent');
      }

      // Process payment
      bool success = await PaymentService.processPayment(
        clientSecret: paymentIntent['clientSecret'],
        context: context,
        paymentMethodId: _selectedPaymentMethodId,
      );

      if (success) {
        // Confirm payment
        bool confirmed = await PaymentService.confirmPayment(
          paymentIntentId: paymentIntent['id'],
          userId: widget.userId,
        );

        if (confirmed) {
          PaymentService.showSuccessDialog(
            context,
            'Payment successful! Your appointment has been confirmed.',
          );
          Navigator.pop(context, true); // Return success
        } else {
          throw Exception('Payment confirmation failed');
        }
      }
    } catch (e) {
      print('Payment error: $e');
      _showErrorDialog('Payment failed. Please try again.');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Payment'),
        backgroundColor: Colors.purple,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Appointment details card
                  Card(
                    elevation: 4,
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Appointment Details',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 12),
                          _buildDetailRow('Therapist', widget.therapistName),
                          _buildDetailRow('Type', widget.appointmentType),
                          _buildDetailRow(
                              'Duration', '${widget.duration} minutes'),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 20),

                  // Cost breakdown card
                  Card(
                    elevation: 4,
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Cost Breakdown',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 12),
                          _buildCostRow(
                            'Session Fee',
                            PaymentService.formatCurrency(
                                _costBreakdown['subtotal'] ?? 0),
                          ),
                          _buildCostRow(
                            'Service Fee',
                            PaymentService.formatCurrency(
                                _costBreakdown['serviceFee'] ?? 0),
                          ),
                          _buildCostRow(
                            'Tax',
                            PaymentService.formatCurrency(
                                _costBreakdown['tax'] ?? 0),
                          ),
                          const Divider(),
                          _buildCostRow(
                            'Total',
                            PaymentService.formatCurrency(
                                _costBreakdown['total'] ?? 0),
                            isTotal: true,
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 20),

                  // Payment methods card
                  Card(
                    elevation: 4,
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Payment Method',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 12),

                          // Saved payment methods
                          if (_savedPaymentMethods.isNotEmpty) ...[
                            const Text(
                              'Saved Payment Methods',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const SizedBox(height: 8),
                            ..._savedPaymentMethods.map(
                              (method) => RadioListTile<String>(
                                title:
                                    Text('**** **** **** ${method['last4']}'),
                                subtitle: Text(
                                    '${method['brand'].toUpperCase()} • Expires ${method['expMonth']}/${method['expYear']}'),
                                value: method['id'],
                                groupValue: _selectedPaymentMethodId,
                                onChanged: (value) {
                                  setState(() {
                                    _selectedPaymentMethodId = value;
                                  });
                                },
                              ),
                            ),
                            const SizedBox(height: 12),
                          ],

                          // Add new payment method option
                          RadioListTile<String>(
                            title: const Text('Add New Payment Method'),
                            subtitle: const Text('Credit or Debit Card'),
                            value: 'new',
                            groupValue: _selectedPaymentMethodId ?? 'new',
                            onChanged: (value) {
                              setState(() {
                                _selectedPaymentMethodId = null;
                              });
                            },
                          ),

                          // Save payment method checkbox
                          if (_selectedPaymentMethodId == null)
                            CheckboxListTile(
                              title: const Text('Save this payment method'),
                              subtitle:
                                  const Text('For faster checkout next time'),
                              value: _savePaymentMethod,
                              onChanged: (value) {
                                setState(() {
                                  _savePaymentMethod = value ?? false;
                                });
                              },
                            ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 30),

                  // Pay button
                  SizedBox(
                    width: double.infinity,
                    height: 50,
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _processPayment,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.purple,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                      child: _isLoading
                          ? const CircularProgressIndicator(color: Colors.white)
                          : Text(
                              'Pay ${PaymentService.formatCurrency(_costBreakdown['total'] ?? 0)}',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Security notice
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        const Icon(Icons.security,
                            color: Colors.green, size: 20),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Your payment information is secure and encrypted',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCostRow(String label, String amount, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? Colors.black : Colors.grey[600],
            ),
          ),
          Text(
            amount,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
              color: isTotal ? Colors.purple : Colors.black,
            ),
          ),
        ],
      ),
    );
  }
}
