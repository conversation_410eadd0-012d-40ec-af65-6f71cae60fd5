const { User } = require('../models');
const bcrypt = require('bcryptjs');
const nodemailer = require('nodemailer');
require('dotenv').config();

class ForgotPasswordController {
  static async forgotPassword(req, res) {
    try {
      const { email } = req.body;

      // Validate email input
      if (!email) {
        return res.status(400).json({ message: "Email is required" });
      }

      // Check if user exists
      const user = await User.findOne({ email });

      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      // Generate a secure random password
      const password = Math.random().toString(36).slice(-8) + Math.random().toString(36).slice(-4).toUpperCase();
      const hashedPassword = await bcrypt.hash(password, 10);

      // Update user password
      user.passwordHash = hashedPassword;
      await user.save();

      // Configure email transporter
      const transporter = nodemailer.createTransport({
        host: process.env.SMTP_HOST,
        port: Number(process.env.SMTP_PORT),
        secure: Number(process.env.SMTP_PORT) === 465, // true if using port 465
        auth: {
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASS,
        },
      });

      // Create HTML email template
      const htmlTemplate = `
        <!DOCTYPE html>
        <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #8B5CF6, #A855F7); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
            .password-box { background: #fff; border: 2px solid #8B5CF6; border-radius: 8px; padding: 20px; margin: 20px 0; text-align: center; }
            .password { font-size: 24px; font-weight: bold; color: #8B5CF6; letter-spacing: 2px; }
            .footer { text-align: center; margin-top: 20px; color: #666; }
            .warning { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🧠 MindEase</h1>
              <h2>Password Reset Request</h2>
            </div>
            <div class="content">
              <p>Hello <strong>${user.username}</strong>,</p>
              <p>We received a request to reset your password for your MindEase account.</p>
              
              <div class="password-box">
                <p>Your new temporary password is:</p>
                <div class="password">${password}</div>
              </div>
              
              <div class="warning">
                <strong>⚠️ Important Security Notice:</strong>
                <ul>
                  <li>Please log in and change this password immediately</li>
                  <li>Do not share this password with anyone</li>
                  <li>This password will expire in 24 hours for security</li>
                </ul>
              </div>
              
              <p>If you didn't request this password reset, please contact our support team immediately.</p>
              
              <div class="footer">
                <p>Best regards,<br><strong>The MindEase Team</strong></p>
                <p><em>Your mental wellness journey matters to us</em></p>
              </div>
            </div>
          </div>
        </body>
        </html>
      `;

      const mailOptions = {
        from: `"MindEase Support" <${process.env.EMAIL_FROM}>`,
        to: user.email,
        subject: "🔐 MindEase - Password Reset Request",
        text: `Hello ${user.username},\n\nYour new temporary password is: ${password}\n\nPlease log in and change this password immediately for security.\n\nIf you didn't request this, please contact support.\n\nBest regards,\nThe MindEase Team`,
        html: htmlTemplate,
      };

      // Send email (with fallback for testing)
      try {
        await transporter.sendMail(mailOptions);
        console.log(`✅ Password reset email sent to: ${email}`);
        res.status(200).json({
          message: "New password sent to your email",
          success: true
        });
      } catch (emailError) {
        console.log(`⚠️ Email sending failed, but password was updated in database`);
        console.log(`🔑 New password for ${email}: ${password}`);
        res.status(200).json({
          message: `Password updated successfully. New password: ${password} (Email service temporarily unavailable)`,
          success: true,
          temporaryPassword: password // Only for testing - remove in production
        });
      }

    } catch (error) {
      console.error("Forgot Password Error:", error.message);
      console.error(error.stack);
      
      // More specific error handling
      if (error.code === 'EAUTH') {
        res.status(500).json({ message: "Email authentication failed. Please contact support." });
      } else if (error.code === 'ECONNECTION') {
        res.status(500).json({ message: "Unable to connect to email service. Please try again later." });
      } else {
        res.status(500).json({ message: "Error in forgot password process. Please try again." });
      }
    }
  }
}

module.exports = ForgotPasswordController;
