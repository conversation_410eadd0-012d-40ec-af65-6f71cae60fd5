import 'package:agora_rtc_engine/agora_rtc_engine.dart';
import 'package:flutter/foundation.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:myapp/api_service.dart';
import 'package:myapp/constant/constant.dart';

class VideoCallService {
  static final VideoCallService _instance = VideoCallService._internal();
  factory VideoCallService() => _instance;
  VideoCallService._internal();

  RtcEngine? _engine;
  bool _isInitialized = false;
  bool _isInCall = false;
  String? _currentChannelName;
  String? _currentToken;

  // Callbacks
  Function(int uid, bool muted)? onUserJoined;
  Function(int uid)? onUserLeft;
  Function(bool muted)? onLocalUserMuted;
  Function(String error)? onError;
  Function()? onCallEnded;

  // Initialize Agora engine
  Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      // Request permissions
      await _requestPermissions();

      // Create Agora engine
      _engine = createAgoraRtcEngine();
      await _engine!.initialize(const RtcEngineContext(
        appId: appId,
        channelProfile: ChannelProfileType.channelProfileCommunication,
      ));

      // Set up event handlers
      _setupEventHandlers();

      // Enable video
      await _engine!.enableVideo();
      await _engine!.enableAudio();

      _isInitialized = true;
      if (kDebugMode) {
        print('✅ Video call service initialized successfully');
      }
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize video call service: $e');
      }
      onError?.call('Failed to initialize video call: $e');
      return false;
    }
  }

  // Request necessary permissions
  Future<void> _requestPermissions() async {
    await [
      Permission.microphone,
      Permission.camera,
    ].request();
  }

  // Set up event handlers
  void _setupEventHandlers() {
    _engine!.registerEventHandler(
      RtcEngineEventHandler(
        onJoinChannelSuccess: (RtcConnection connection, int elapsed) {
          if (kDebugMode) {
            print('✅ Successfully joined channel: ${connection.channelId}');
          }
          _isInCall = true;
        },
        onUserJoined: (RtcConnection connection, int remoteUid, int elapsed) {
          if (kDebugMode) {
            print('👤 User joined: $remoteUid');
          }
          onUserJoined?.call(remoteUid, false);
        },
        onUserOffline: (RtcConnection connection, int remoteUid, UserOfflineReasonType reason) {
          if (kDebugMode) {
            print('👋 User left: $remoteUid');
          }
          onUserLeft?.call(remoteUid);
        },
        onTokenPrivilegeWillExpire: (RtcConnection connection, String token) {
          if (kDebugMode) {
            print('⚠️ Token will expire, renewing...');
          }
          _renewToken();
        },
        onError: (ErrorCodeType err, String msg) {
          if (kDebugMode) {
            print('❌ Agora error: $err - $msg');
          }
          onError?.call('Video call error: $msg');
        },
        onLeaveChannel: (RtcConnection connection, RtcStats stats) {
          if (kDebugMode) {
            print('📞 Left channel');
          }
          _isInCall = false;
          onCallEnded?.call();
        },
      ),
    );
  }

  // Join a video call
  Future<bool> joinCall({
    required String channelName,
    required String userId,
    String? customToken,
  }) async {
    if (!_isInitialized) {
      final initialized = await initialize();
      if (!initialized) return false;
    }

    try {
      _currentChannelName = channelName;
      
      // Get token from API if not provided
      if (customToken == null) {
        final tokenResult = await ApiService.getAgoraToken(
          channelName: channelName,
          userId: userId,
        );
        
        if (tokenResult['success']) {
          _currentToken = tokenResult['data']['token'];
        } else {
          // Fallback to default token for demo
          _currentToken = token;
          if (kDebugMode) {
            print('⚠️ Using fallback token for demo purposes');
          }
        }
      } else {
        _currentToken = customToken;
      }

      // Join channel
      await _engine!.joinChannel(
        token: _currentToken!,
        channelId: channelName,
        uid: int.tryParse(userId) ?? 0,
        options: const ChannelMediaOptions(
          clientRoleType: ClientRoleType.clientRoleBroadcaster,
          channelProfile: ChannelProfileType.channelProfileCommunication,
        ),
      );

      if (kDebugMode) {
        print('🎥 Joining video call: $channelName');
      }
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to join call: $e');
      }
      onError?.call('Failed to join call: $e');
      return false;
    }
  }

  // Leave the current call
  Future<void> leaveCall() async {
    if (_engine != null && _isInCall) {
      await _engine!.leaveChannel();
      _currentChannelName = null;
      _currentToken = null;
      _isInCall = false;
      
      if (kDebugMode) {
        print('📞 Left video call');
      }
    }
  }

  // Toggle local video
  Future<void> toggleVideo(bool enabled) async {
    if (_engine != null) {
      await _engine!.enableLocalVideo(enabled);
      if (kDebugMode) {
        print('📹 Video ${enabled ? 'enabled' : 'disabled'}');
      }
    }
  }

  // Toggle local audio
  Future<void> toggleAudio(bool enabled) async {
    if (_engine != null) {
      await _engine!.enableLocalAudio(enabled);
      onLocalUserMuted?.call(!enabled);
      if (kDebugMode) {
        print('🎤 Audio ${enabled ? 'enabled' : 'disabled'}');
      }
    }
  }

  // Switch camera
  Future<void> switchCamera() async {
    if (_engine != null) {
      await _engine!.switchCamera();
      if (kDebugMode) {
        print('🔄 Camera switched');
      }
    }
  }

  // Renew token when it's about to expire
  Future<void> _renewToken() async {
    if (_currentChannelName == null) return;

    try {
      final tokenResult = await ApiService.getAgoraToken(
        channelName: _currentChannelName!,
        userId: '0', // Use default user ID for renewal
      );
      
      if (tokenResult['success']) {
        final newToken = tokenResult['data']['token'];
        await _engine!.renewToken(newToken);
        _currentToken = newToken;
        
        if (kDebugMode) {
          print('🔄 Token renewed successfully');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to renew token: $e');
      }
    }
  }

  // Create a video widget for local preview
  Widget? createLocalVideoWidget() {
    if (_engine == null) return null;
    return AgoraVideoView(
      controller: VideoViewController(
        rtcEngine: _engine!,
        canvas: const VideoCanvas(uid: 0),
      ),
    );
  }

  // Create a video widget for remote user
  Widget? createRemoteVideoWidget(int uid) {
    if (_engine == null) return null;
    return AgoraVideoView(
      controller: VideoViewController.remote(
        rtcEngine: _engine!,
        canvas: VideoCanvas(uid: uid),
        connection: RtcConnection(channelId: _currentChannelName),
      ),
    );
  }

  // Get call statistics
  Future<Map<String, dynamic>?> getCallStats() async {
    if (_engine == null || !_isInCall) return null;

    try {
      final stats = await _engine!.getCallStats();
      return {
        'duration': stats.duration,
        'txBytes': stats.txBytes,
        'rxBytes': stats.rxBytes,
        'txKBitRate': stats.txKBitrate,
        'rxKBitRate': stats.rxKBitrate,
        'users': stats.users,
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to get call stats: $e');
      }
      return null;
    }
  }

  // Dispose the service
  Future<void> dispose() async {
    if (_isInCall) {
      await leaveCall();
    }
    
    if (_engine != null) {
      await _engine!.release();
      _engine = null;
    }
    
    _isInitialized = false;
    if (kDebugMode) {
      print('🗑️ Video call service disposed');
    }
  }

  // Getters
  bool get isInitialized => _isInitialized;
  bool get isInCall => _isInCall;
  String? get currentChannelName => _currentChannelName;
  RtcEngine? get engine => _engine;
}
