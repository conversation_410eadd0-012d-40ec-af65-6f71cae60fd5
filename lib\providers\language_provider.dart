import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LanguageProvider extends ChangeNotifier {
  String _currentLanguage = 'en';
  
  String get currentLanguage => _currentLanguage;
  
  final Map<String, Map<String, String>> _translations = {
    'en': {
      'app_name': 'MindEase',
      'welcome': 'Welcome',
      'login': 'Login',
      'signup': 'Sign Up',
      'email': 'Email',
      'password': 'Password',
      'forgot_password': 'Forgot Password?',
      'mood_tracking': 'Mood Tracking',
      'journaling': 'Journaling',
      'find_therapists': 'Find Therapists',
      'video_consultation': 'Video Consultation',
      'settings': 'Settings',
      'language': 'Language',
      'logout': 'Logout',
      'save': 'Save',
      'cancel': 'Cancel',
      'how_feeling_today': 'How are you feeling today?',
      'track_mood': 'Track Mood',
      'write_journal': 'Write Journal',
      'book_appointment': 'Book Appointment',
      'nearby_therapists': 'Nearby Therapists',
      'mood_insights': 'Mood Insights',
      'quick_actions': 'Quick Actions',
      'explore_features': 'Explore Features',
      'your_wellness_companion': 'Your mental wellness companion',
      'connect_professionals': 'Connect with qualified mental health professionals',
      'search_therapists': 'Search therapists or specializations...',
      'finding_therapists': 'Finding therapists near you...',
      'no_therapists_found': 'No therapists found',
      'try_adjusting_search': 'Try adjusting your search criteria',
      'book_now': 'Book Now',
      'available': 'Available',
      'busy': 'Busy',
      'experience': 'experience',
      'session': 'session',
      'today': 'Today',
      'tomorrow': 'Tomorrow',
      'next_week': 'Next week',
      'appointment_booked': 'Appointment booked successfully!',
      'confirm': 'Confirm',
      'loading_data': 'Loading your wellness dashboard...',
      'no_mood_entries': 'No mood entries yet',
      'start_tracking_mood': 'Start tracking your mood to see insights here',
      'recent_mood_insights': 'Recent Mood Insights',
      'professional_theme': 'Professional light theme',
      'theme': 'Theme',
    },
    'es': {
      'app_name': 'MindEase',
      'welcome': 'Bienvenido',
      'login': 'Iniciar Sesión',
      'signup': 'Registrarse',
      'email': 'Correo Electrónico',
      'password': 'Contraseña',
      'forgot_password': '¿Olvidaste tu contraseña?',
      'mood_tracking': 'Seguimiento del Estado de Ánimo',
      'journaling': 'Diario Personal',
      'find_therapists': 'Encontrar Terapeutas',
      'video_consultation': 'Consulta por Video',
      'settings': 'Configuración',
      'language': 'Idioma',
      'logout': 'Cerrar Sesión',
      'save': 'Guardar',
      'cancel': 'Cancelar',
      'how_feeling_today': '¿Cómo te sientes hoy?',
      'track_mood': 'Seguir Estado de Ánimo',
      'write_journal': 'Escribir Diario',
      'book_appointment': 'Reservar Cita',
      'nearby_therapists': 'Terapeutas Cercanos',
      'mood_insights': 'Perspectivas del Estado de Ánimo',
      'quick_actions': 'Acciones Rápidas',
      'explore_features': 'Explorar Características',
      'your_wellness_companion': 'Tu compañero de bienestar mental',
      'connect_professionals': 'Conecta con profesionales de salud mental calificados',
      'search_therapists': 'Buscar terapeutas o especializaciones...',
      'finding_therapists': 'Encontrando terapeutas cerca de ti...',
      'no_therapists_found': 'No se encontraron terapeutas',
      'try_adjusting_search': 'Intenta ajustar tus criterios de búsqueda',
      'book_now': 'Reservar Ahora',
      'available': 'Disponible',
      'busy': 'Ocupado',
      'experience': 'experiencia',
      'session': 'sesión',
      'today': 'Hoy',
      'tomorrow': 'Mañana',
      'next_week': 'Próxima semana',
      'appointment_booked': '¡Cita reservada exitosamente!',
      'confirm': 'Confirmar',
      'loading_data': 'Cargando tu panel de bienestar...',
      'no_mood_entries': 'Aún no hay entradas de estado de ánimo',
      'start_tracking_mood': 'Comienza a rastrear tu estado de ánimo para ver perspectivas aquí',
      'recent_mood_insights': 'Perspectivas Recientes del Estado de Ánimo',
      'professional_theme': 'Tema profesional claro',
      'theme': 'Tema',
    },
    'fr': {
      'app_name': 'MindEase',
      'welcome': 'Bienvenue',
      'login': 'Se Connecter',
      'signup': 'S\'inscrire',
      'email': 'Email',
      'password': 'Mot de Passe',
      'forgot_password': 'Mot de passe oublié?',
      'mood_tracking': 'Suivi de l\'Humeur',
      'journaling': 'Journal Personnel',
      'find_therapists': 'Trouver des Thérapeutes',
      'video_consultation': 'Consultation Vidéo',
      'settings': 'Paramètres',
      'language': 'Langue',
      'logout': 'Se Déconnecter',
      'save': 'Sauvegarder',
      'cancel': 'Annuler',
      'how_feeling_today': 'Comment vous sentez-vous aujourd\'hui?',
      'track_mood': 'Suivre l\'Humeur',
      'write_journal': 'Écrire un Journal',
      'book_appointment': 'Prendre Rendez-vous',
      'nearby_therapists': 'Thérapeutes à Proximité',
      'mood_insights': 'Aperçus de l\'Humeur',
      'quick_actions': 'Actions Rapides',
      'explore_features': 'Explorer les Fonctionnalités',
      'your_wellness_companion': 'Votre compagnon de bien-être mental',
      'connect_professionals': 'Connectez-vous avec des professionnels de la santé mentale qualifiés',
      'search_therapists': 'Rechercher des thérapeutes ou des spécialisations...',
      'finding_therapists': 'Recherche de thérapeutes près de chez vous...',
      'no_therapists_found': 'Aucun thérapeute trouvé',
      'try_adjusting_search': 'Essayez d\'ajuster vos critères de recherche',
      'book_now': 'Réserver Maintenant',
      'available': 'Disponible',
      'busy': 'Occupé',
      'experience': 'expérience',
      'session': 'séance',
      'today': 'Aujourd\'hui',
      'tomorrow': 'Demain',
      'next_week': 'Semaine prochaine',
      'appointment_booked': 'Rendez-vous réservé avec succès!',
      'confirm': 'Confirmer',
      'loading_data': 'Chargement de votre tableau de bord bien-être...',
      'no_mood_entries': 'Aucune entrée d\'humeur pour le moment',
      'start_tracking_mood': 'Commencez à suivre votre humeur pour voir les aperçus ici',
      'recent_mood_insights': 'Aperçus Récents de l\'Humeur',
      'professional_theme': 'Thème professionnel clair',
      'theme': 'Thème',
    },
    'ar': {
      'app_name': 'MindEase',
      'welcome': 'مرحباً',
      'login': 'تسجيل الدخول',
      'signup': 'إنشاء حساب',
      'email': 'البريد الإلكتروني',
      'password': 'كلمة المرور',
      'forgot_password': 'نسيت كلمة المرور؟',
      'mood_tracking': 'تتبع المزاج',
      'journaling': 'كتابة اليوميات',
      'find_therapists': 'العثور على المعالجين',
      'video_consultation': 'استشارة فيديو',
      'settings': 'الإعدادات',
      'language': 'اللغة',
      'logout': 'تسجيل الخروج',
      'save': 'حفظ',
      'cancel': 'إلغاء',
      'how_feeling_today': 'كيف تشعر اليوم؟',
      'track_mood': 'تتبع المزاج',
      'write_journal': 'كتابة يومية',
      'book_appointment': 'حجز موعد',
      'nearby_therapists': 'المعالجون القريبون',
      'mood_insights': 'رؤى المزاج',
      'quick_actions': 'إجراءات سريعة',
      'explore_features': 'استكشاف الميزات',
      'your_wellness_companion': 'رفيقك في الصحة النفسية',
      'connect_professionals': 'تواصل مع متخصصين مؤهلين في الصحة النفسية',
      'search_therapists': 'البحث عن معالجين أو تخصصات...',
      'finding_therapists': 'البحث عن معالجين بالقرب منك...',
      'no_therapists_found': 'لم يتم العثور على معالجين',
      'try_adjusting_search': 'حاول تعديل معايير البحث',
      'book_now': 'احجز الآن',
      'available': 'متاح',
      'busy': 'مشغول',
      'experience': 'خبرة',
      'session': 'جلسة',
      'today': 'اليوم',
      'tomorrow': 'غداً',
      'next_week': 'الأسبوع القادم',
      'appointment_booked': 'تم حجز الموعد بنجاح!',
      'confirm': 'تأكيد',
      'loading_data': 'تحميل لوحة معلومات الصحة...',
      'no_mood_entries': 'لا توجد إدخالات مزاج بعد',
      'start_tracking_mood': 'ابدأ بتتبع مزاجك لرؤية الرؤى هنا',
      'recent_mood_insights': 'رؤى المزاج الحديثة',
      'professional_theme': 'موضوع مهني فاتح',
      'theme': 'الموضوع',
    },
  };

  final List<Map<String, String>> supportedLanguages = [
    {'code': 'en', 'name': 'English', 'nativeName': 'English'},
    {'code': 'es', 'name': 'Spanish', 'nativeName': 'Español'},
    {'code': 'fr', 'name': 'French', 'nativeName': 'Français'},
    {'code': 'ar', 'name': 'Arabic', 'nativeName': 'العربية'},
  ];

  LanguageProvider() {
    _loadLanguage();
  }

  String translate(String key) {
    return _translations[_currentLanguage]?[key] ?? key;
  }

  Future<void> changeLanguage(String languageCode) async {
    if (_translations.containsKey(languageCode)) {
      _currentLanguage = languageCode;
      await _saveLanguage();
      notifyListeners();
    }
  }

  Future<void> _loadLanguage() async {
    final prefs = await SharedPreferences.getInstance();
    _currentLanguage = prefs.getString('language') ?? 'en';
    notifyListeners();
  }

  Future<void> _saveLanguage() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('language', _currentLanguage);
  }

  String getCurrentLanguageName() {
    final language = supportedLanguages.firstWhere(
      (lang) => lang['code'] == _currentLanguage,
      orElse: () => supportedLanguages.first,
    );
    return language['nativeName'] ?? 'English';
  }
}
