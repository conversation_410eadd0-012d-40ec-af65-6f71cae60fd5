import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:myapp/services/therapist_logger.dart';

class TherapistLogsScreen extends StatefulWidget {
  final String therapistId;

  const TherapistLogsScreen({super.key, required this.therapistId});

  @override
  State<TherapistLogsScreen> createState() => _TherapistLogsScreenState();
}

class _TherapistLogsScreenState extends State<TherapistLogsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final TherapistLogger _logger = TherapistLogger();

  LogLevel? _selectedLevel;
  LogCategory? _selectedCategory;
  DateTime? _startDate;
  DateTime? _endDate;

  List<LogEntry> _filteredLogs = [];
  Map<String, dynamic> _statistics = {};

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadLogs();
    _loadStatistics();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadLogs() {
    setState(() {
      _filteredLogs = _logger.getLogs(
        level: _selectedLevel,
        category: _selectedCategory,
        startDate: _startDate,
        endDate: _endDate,
        limit: 100,
      );
    });
  }

  void _loadStatistics() {
    setState(() {
      _statistics = _logger.getLogStatistics();
    });
  }

  void _clearFilters() {
    setState(() {
      _selectedLevel = null;
      _selectedCategory = null;
      _startDate = null;
      _endDate = null;
    });
    _loadLogs();
  }

  Future<void> _exportLogs() async {
    final logs = _logger.exportLogs(
      level: _selectedLevel,
      category: _selectedCategory,
      startDate: _startDate,
      endDate: _endDate,
    );

    await Clipboard.setData(ClipboardData(text: logs));

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Logs exported to clipboard'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  Future<void> _clearAllLogs() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Logs'),
        content: const Text(
            'Are you sure you want to clear all logs? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Clear'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _logger.clearLogs();
      _loadLogs();
      _loadStatistics();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('All logs cleared'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Activity Logs'),
        backgroundColor: const Color(0xFF8B5CF6),
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(text: 'Logs', icon: Icon(Icons.list)),
            Tab(text: 'Statistics', icon: Icon(Icons.analytics)),
            Tab(text: 'Filters', icon: Icon(Icons.filter_list)),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.download),
            onPressed: _exportLogs,
            tooltip: 'Export Logs',
          ),
          IconButton(
            icon: const Icon(Icons.delete_sweep),
            onPressed: _clearAllLogs,
            tooltip: 'Clear All Logs',
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildLogsTab(),
          _buildStatisticsTab(),
          _buildFiltersTab(),
        ],
      ),
    );
  }

  Widget _buildLogsTab() {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          color: Colors.grey[100],
          child: Row(
            children: [
              Icon(Icons.info_outline, color: Colors.grey[600]),
              const SizedBox(width: 8),
              Text(
                'Showing ${_filteredLogs.length} log entries',
                style: TextStyle(color: Colors.grey[600]),
              ),
              const Spacer(),
              if (_selectedLevel != null ||
                  _selectedCategory != null ||
                  _startDate != null ||
                  _endDate != null)
                TextButton.icon(
                  onPressed: _clearFilters,
                  icon: const Icon(Icons.clear),
                  label: const Text('Clear Filters'),
                ),
            ],
          ),
        ),
        Expanded(
          child: _filteredLogs.isEmpty
              ? const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.inbox, size: 64, color: Colors.grey),
                      SizedBox(height: 16),
                      Text(
                        'No logs found',
                        style: TextStyle(fontSize: 18, color: Colors.grey),
                      ),
                      Text(
                        'Try adjusting your filters',
                        style: TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  itemCount: _filteredLogs.length,
                  itemBuilder: (context, index) {
                    final log = _filteredLogs[index];
                    return _buildLogItem(log);
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildLogItem(LogEntry log) {
    final levelColor = _getLevelColor(log.level);
    final categoryIcon = _getCategoryIcon(log.category);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ExpansionTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: levelColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(categoryIcon, color: levelColor),
        ),
        title: Text(
          log.message,
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Row(
              children: [
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: levelColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    log.level.name.toUpperCase(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  log.category.name.toUpperCase(),
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              DateFormat('MMM dd, yyyy HH:mm:ss').format(log.timestamp),
              style: TextStyle(color: Colors.grey[600], fontSize: 12),
            ),
          ],
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (log.metadata != null) ...[
                  const Text(
                    'Metadata:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      _formatMetadata(log.metadata!),
                      style: const TextStyle(fontFamily: 'monospace'),
                    ),
                  ),
                ],
                if (log.stackTrace != null) ...[
                  const SizedBox(height: 16),
                  const Text(
                    'Stack Trace:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.red[50],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      log.stackTrace!,
                      style: const TextStyle(
                        fontFamily: 'monospace',
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStatCard(
            'Total Logs',
            _statistics['totalLogs']?.toString() ?? '0',
            Icons.list_alt,
            Colors.blue,
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Today',
                  _statistics['todayLogs']?.toString() ?? '0',
                  Icons.today,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  'This Week',
                  _statistics['thisWeekLogs']?.toString() ?? '0',
                  Icons.date_range,
                  Colors.orange,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildStatCard(
            'Errors',
            _statistics['errorLogs']?.toString() ?? '0',
            Icons.error,
            Colors.red,
          ),
          const SizedBox(height: 24),
          const Text(
            'Log Categories',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          ..._buildCategoryStats(),
          const SizedBox(height: 24),
          const Text(
            'Log Levels',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          ..._buildLevelStats(),
        ],
      ),
    );
  }

  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(width: 16),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  title,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildCategoryStats() {
    final categories = _statistics['categories'] as List? ?? [];
    return categories.map<Widget>((cat) {
      final name = cat['category'] as String;
      final count = cat['count'] as int;
      return ListTile(
        leading: Icon(_getCategoryIcon(
            LogCategory.values.firstWhere((e) => e.name == name))),
        title: Text(name.toUpperCase()),
        trailing: Text(count.toString()),
      );
    }).toList();
  }

  List<Widget> _buildLevelStats() {
    final levels = _statistics['levels'] as List? ?? [];
    return levels.map<Widget>((level) {
      final name = level['level'] as String;
      final count = level['count'] as int;
      final color =
          _getLevelColor(LogLevel.values.firstWhere((e) => e.name == name));
      return ListTile(
        leading: Container(
          width: 20,
          height: 20,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        title: Text(name.toUpperCase()),
        trailing: Text(count.toString()),
      );
    }).toList();
  }

  Widget _buildFiltersTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Filter Logs',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 24),

          // Log Level Filter
          const Text('Log Level',
              style: TextStyle(fontWeight: FontWeight.w500)),
          const SizedBox(height: 8),
          DropdownButtonFormField<LogLevel>(
            value: _selectedLevel,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: 'Select log level',
            ),
            items: [
              const DropdownMenuItem<LogLevel>(
                value: null,
                child: Text('All Levels'),
              ),
              ...LogLevel.values.map((level) => DropdownMenuItem(
                    value: level,
                    child: Text(level.name.toUpperCase()),
                  )),
            ],
            onChanged: (value) {
              setState(() {
                _selectedLevel = value;
              });
              _loadLogs();
            },
          ),

          const SizedBox(height: 16),

          // Category Filter
          const Text('Category', style: TextStyle(fontWeight: FontWeight.w500)),
          const SizedBox(height: 8),
          DropdownButtonFormField<LogCategory>(
            value: _selectedCategory,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: 'Select category',
            ),
            items: [
              const DropdownMenuItem<LogCategory>(
                value: null,
                child: Text('All Categories'),
              ),
              ...LogCategory.values.map((category) => DropdownMenuItem(
                    value: category,
                    child: Text(category.name.toUpperCase()),
                  )),
            ],
            onChanged: (value) {
              setState(() {
                _selectedCategory = value;
              });
              _loadLogs();
            },
          ),

          const SizedBox(height: 16),

          // Date Range Filter
          const Text('Date Range',
              style: TextStyle(fontWeight: FontWeight.w500)),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                    labelText: 'Start Date',
                    suffixIcon: Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  controller: TextEditingController(
                    text: _startDate != null
                        ? DateFormat('MMM dd, yyyy').format(_startDate!)
                        : '',
                  ),
                  onTap: () async {
                    final date = await showDatePicker(
                      context: context,
                      initialDate: _startDate ?? DateTime.now(),
                      firstDate:
                          DateTime.now().subtract(const Duration(days: 365)),
                      lastDate: DateTime.now(),
                    );
                    if (date != null) {
                      setState(() {
                        _startDate = date;
                      });
                      _loadLogs();
                    }
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                    labelText: 'End Date',
                    suffixIcon: Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  controller: TextEditingController(
                    text: _endDate != null
                        ? DateFormat('MMM dd, yyyy').format(_endDate!)
                        : '',
                  ),
                  onTap: () async {
                    final date = await showDatePicker(
                      context: context,
                      initialDate: _endDate ?? DateTime.now(),
                      firstDate: _startDate ??
                          DateTime.now().subtract(const Duration(days: 365)),
                      lastDate: DateTime.now(),
                    );
                    if (date != null) {
                      setState(() {
                        _endDate = date;
                      });
                      _loadLogs();
                    }
                  },
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Quick Filter Buttons
          const Text('Quick Filters',
              style: TextStyle(fontWeight: FontWeight.w500)),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              _buildQuickFilterChip('Today', () {
                final today = DateTime.now();
                setState(() {
                  _startDate = DateTime(today.year, today.month, today.day);
                  _endDate =
                      DateTime(today.year, today.month, today.day, 23, 59, 59);
                });
                _loadLogs();
              }),
              _buildQuickFilterChip('This Week', () {
                final now = DateTime.now();
                final startOfWeek =
                    now.subtract(Duration(days: now.weekday - 1));
                setState(() {
                  _startDate = DateTime(
                      startOfWeek.year, startOfWeek.month, startOfWeek.day);
                  _endDate = DateTime.now();
                });
                _loadLogs();
              }),
              _buildQuickFilterChip('Errors Only', () {
                setState(() {
                  _selectedLevel = LogLevel.error;
                });
                _loadLogs();
              }),
              _buildQuickFilterChip('API Calls', () {
                setState(() {
                  _selectedCategory = LogCategory.apiCall;
                });
                _loadLogs();
              }),
            ],
          ),

          const SizedBox(height: 24),

          // Clear Filters Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _clearFilters,
              icon: const Icon(Icons.clear_all),
              label: const Text('Clear All Filters'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey[200],
                foregroundColor: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickFilterChip(String label, VoidCallback onPressed) {
    return ActionChip(
      label: Text(label),
      onPressed: onPressed,
      backgroundColor: const Color(0xFF8B5CF6).withOpacity(0.1),
      labelStyle: const TextStyle(color: Color(0xFF8B5CF6)),
    );
  }

  Color _getLevelColor(LogLevel level) {
    switch (level) {
      case LogLevel.debug:
        return Colors.grey;
      case LogLevel.info:
        return Colors.blue;
      case LogLevel.warning:
        return Colors.orange;
      case LogLevel.error:
        return Colors.red;
      case LogLevel.critical:
        return Colors.purple;
    }
  }

  IconData _getCategoryIcon(LogCategory category) {
    switch (category) {
      case LogCategory.authentication:
        return Icons.login;
      case LogCategory.dataAccess:
        return Icons.storage;
      case LogCategory.userInteraction:
        return Icons.touch_app;
      case LogCategory.apiCall:
        return Icons.api;
      case LogCategory.systemEvent:
        return Icons.settings;
      case LogCategory.security:
        return Icons.security;
      case LogCategory.performance:
        return Icons.speed;
    }
  }

  String _formatMetadata(Map<String, dynamic> metadata) {
    final buffer = StringBuffer();
    metadata.forEach((key, value) {
      buffer.writeln('$key: $value');
    });
    return buffer.toString().trim();
  }
}
