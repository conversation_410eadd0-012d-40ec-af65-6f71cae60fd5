import 'package:flutter/material.dart';
import 'package:myapp/api_service.dart';
import 'package:myapp/mock_api_service.dart';
import 'package:myapp/therapist_dashboard.dart';

class TestTherapistLogin extends StatefulWidget {
  const TestTherapistLogin({super.key});

  @override
  State<TestTherapistLogin> createState() => _TestTherapistLoginState();
}

class _TestTherapistLoginState extends State<TestTherapistLogin> {
  final TextEditingController emailController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  String result = '';
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    // Pre-fill with test credentials
    emailController.text = '<EMAIL>';
    passwordController.text = 'password123';
  }

  Future<void> testTherapistLogin() async {
    setState(() {
      isLoading = true;
      result = 'Testing therapist login...';
    });

    try {
      // Test therapist login
      final loginResult = await ApiService.login(
        emailController.text,
        passwordController.text,
        'therapist',
      );

      setState(() {
        result = 'Login Result:\n${loginResult.toString()}';
        isLoading = false;
      });

      // If login is successful, test navigation to therapist dashboard
      if (loginResult['success'] == true) {
        setState(() {
          result += '\n\n✅ Login successful! Testing navigation...';
        });

        // Test navigation to therapist dashboard
        await Future.delayed(const Duration(seconds: 2));
        if (mounted) {
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => const TherapistDashboard(
                therapistId: 'test-therapist-id',
              ),
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        result = 'Error: $e';
        isLoading = false;
      });
    }
  }

  Future<void> testGetAllTherapists() async {
    setState(() {
      isLoading = true;
      result = 'Getting all therapists...';
    });

    try {
      final therapistsResult = await ApiService.getAllTherapists();
      setState(() {
        result = 'Therapists Result:\n${therapistsResult.toString()}';
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        result = 'Error: $e';
        isLoading = false;
      });
    }
  }

  Future<void> createTestTherapist() async {
    setState(() {
      isLoading = true;
      result = 'Creating test therapist...';
    });

    try {
      // Create a test therapist using the signup endpoint
      final signupResult = await ApiService.signup(
        'Dr. Test Therapist',
        emailController.text,
        passwordController.text,
        'therapist',
      );

      setState(() {
        result = 'Therapist Creation Result:\n${signupResult.toString()}';
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        result = 'Error creating therapist: $e';
        isLoading = false;
      });
    }
  }

  Future<void> testWithMockAPI() async {
    setState(() {
      isLoading = true;
      result = 'Testing with Mock API...';
    });

    try {
      // Test therapist login with mock API
      final loginResult = await MockApiService.login(
        emailController.text,
        passwordController.text,
        'therapist',
      );

      setState(() {
        result = 'Mock API Login Result:\n${loginResult.toString()}';
        isLoading = false;
      });

      // If login is successful, test navigation to therapist dashboard
      if (loginResult['success'] == true) {
        setState(() {
          result += '\n\n✅ Mock login successful! Testing navigation...';
        });

        // Test navigation to therapist dashboard
        await Future.delayed(const Duration(seconds: 2));
        if (mounted) {
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => const TherapistDashboard(
                therapistId: 'therapist-1',
              ),
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        result = 'Error with mock API: $e';
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Therapist Login'),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Test Therapist Login Functionality',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            TextField(
              controller: emailController,
              decoration: const InputDecoration(
                labelText: 'Email',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: passwordController,
              decoration: const InputDecoration(
                labelText: 'Password',
                border: OutlineInputBorder(),
              ),
              obscureText: true,
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: isLoading ? null : testTherapistLogin,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.teal,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: isLoading
                  ? const CircularProgressIndicator(color: Colors.white)
                  : const Text('Test Therapist Login'),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: isLoading ? null : testGetAllTherapists,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: const Text('Get All Therapists'),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: isLoading ? null : createTestTherapist,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: const Text('Create Test Therapist'),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: isLoading ? null : testWithMockAPI,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: const Text('🚀 Test with Mock API (Offline)'),
            ),
            const SizedBox(height: 20),
            const Text(
              'Result:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: SingleChildScrollView(
                  child: Text(
                    result.isEmpty ? 'No result yet' : result,
                    style: const TextStyle(fontFamily: 'monospace'),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    emailController.dispose();
    passwordController.dispose();
    super.dispose();
  }
}
