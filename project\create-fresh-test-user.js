const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/mindease', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

// Define User schema (simplified)
const userSchema = new mongoose.Schema({
  username: String,
  email: String,
  passwordHash: String,
  role: String,
  moodEntries: [{ type: mongoose.Schema.Types.Mixed }],
  recommendations: [{ type: mongoose.Schema.Types.Mixed }],
  paymentIds: [{ type: mongoose.Schema.Types.Mixed }],
  location: {
    type: {
      type: String,
      enum: ['Point'],
      default: 'Point'
    },
    coordinates: {
      type: [Number],
      default: [-74.006, 40.7128]
    }
  }
});

const User = mongoose.model('User', userSchema);

async function createFreshTestUser() {
  try {
    console.log('🔄 Creating fresh test user...');
    
    // Delete existing test user if exists
    await User.deleteOne({ email: '<EMAIL>' });
    console.log('🗑️ Deleted existing fresh test user (if any)');
    
    // Hash the password
    const password = 'test123';
    const hashedPassword = await bcrypt.hash(password, 10);
    
    // Create new user
    const newUser = new User({
      username: 'fresh_test_user',
      email: '<EMAIL>',
      passwordHash: hashedPassword,
      role: 'user',
      moodEntries: [],
      recommendations: [],
      paymentIds: [],
      location: {
        type: 'Point',
        coordinates: [-74.006, 40.7128]
      }
    });
    
    await newUser.save();
    
    console.log('✅ Fresh test user created successfully!');
    console.log(`📧 Email: <EMAIL>`);
    console.log(`🔑 Password: test123`);
    console.log(`👤 Role: user`);
    console.log(`🆔 ID: ${newUser._id}`);
    
  } catch (error) {
    console.error('❌ Error creating user:', error);
  } finally {
    mongoose.connection.close();
    console.log('🔌 Database connection closed');
  }
}

createFreshTestUser();
