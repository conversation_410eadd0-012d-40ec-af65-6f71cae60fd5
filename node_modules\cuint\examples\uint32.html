<html>
<body>
	<div>
		<div id='data'></div>
	</div>
	<script type="text/javascript" src="../build/uint32.lmd.js"></script>
	<script type="text/javascript">
		var v1 = UINT32('326648991')
		var v2 = UINT32('265443576')
		var v1plus2 = v1.clone().add(v2)
		document.getElementById('data').innerHTML += '<p>' + v1 + ' + ' + v2 + ' = ' + v1plus2 + '</p>'

		var v1 = UINT32('3266489917')
		var v2 = UINT32('668265263')
		var v1div2 = v1.clone().div(v2)
		document.getElementById('data').innerHTML += '<p>' + v1 + ' / ' + v2 + ' = ' + v1div2 + '</p>'
	</script>
</body>
</html>