import 'package:flutter/foundation.dart';
import 'package:myapp/api_service.dart';

class SemanticAnalysisService {
  static final SemanticAnalysisService _instance = SemanticAnalysisService._internal();
  factory SemanticAnalysisService() => _instance;
  SemanticAnalysisService._internal();

  // Analyze mood from text
  Future<MoodAnalysisResult?> analyzeMood(String text) async {
    try {
      if (text.trim().isEmpty) {
        throw Exception('Text cannot be empty');
      }

      final result = await ApiService.analyzeMood(text);
      
      if (result['success']) {
        final data = result['data'];
        return MoodAnalysisResult.fromJson(data);
      } else {
        throw Exception(result['error'] ?? 'Failed to analyze mood');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to analyze mood: $e');
      }
      return null;
    }
  }

  // Analyze journal entry
  Future<JournalAnalysisResult?> analyzeJournal(String userId, String content) async {
    try {
      if (content.trim().isEmpty) {
        throw Exception('Journal content cannot be empty');
      }

      final result = await ApiService.analyzeJournal(userId, content);
      
      if (result['success']) {
        final data = result['data'];
        return JournalAnalysisResult.fromJson(data);
      } else {
        throw Exception(result['error'] ?? 'Failed to analyze journal');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to analyze journal: $e');
      }
      return null;
    }
  }

  // Get mood trends for user
  Future<MoodTrendsResult?> getMoodTrends(String userId) async {
    try {
      final result = await ApiService.getMoodTrends(userId);
      
      if (result['success']) {
        final data = result['data'];
        return MoodTrendsResult.fromJson(data);
      } else {
        throw Exception(result['error'] ?? 'Failed to get mood trends');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to get mood trends: $e');
      }
      return null;
    }
  }

  // Analyze text sentiment (local analysis)
  SentimentResult analyzeTextSentiment(String text) {
    if (text.trim().isEmpty) {
      return SentimentResult(
        sentiment: 'neutral',
        confidence: 0.0,
        keywords: [],
      );
    }

    // Simple keyword-based sentiment analysis
    final positiveWords = [
      'happy', 'joy', 'excited', 'great', 'amazing', 'wonderful', 'fantastic',
      'good', 'excellent', 'perfect', 'love', 'beautiful', 'awesome', 'brilliant',
      'cheerful', 'delighted', 'pleased', 'satisfied', 'grateful', 'optimistic',
      'confident', 'peaceful', 'calm', 'relaxed', 'content', 'hopeful'
    ];

    final negativeWords = [
      'sad', 'angry', 'depressed', 'anxious', 'worried', 'stressed', 'upset',
      'frustrated', 'disappointed', 'lonely', 'tired', 'exhausted', 'overwhelmed',
      'hopeless', 'helpless', 'afraid', 'scared', 'nervous', 'irritated',
      'annoyed', 'bitter', 'resentful', 'guilty', 'ashamed', 'regretful'
    ];

    final words = text.toLowerCase().split(RegExp(r'\W+'));
    
    int positiveCount = 0;
    int negativeCount = 0;
    List<String> foundKeywords = [];

    for (String word in words) {
      if (positiveWords.contains(word)) {
        positiveCount++;
        foundKeywords.add(word);
      } else if (negativeWords.contains(word)) {
        negativeCount++;
        foundKeywords.add(word);
      }
    }

    String sentiment;
    double confidence;

    if (positiveCount > negativeCount) {
      sentiment = 'positive';
      confidence = (positiveCount / (positiveCount + negativeCount + 1)).clamp(0.0, 1.0);
    } else if (negativeCount > positiveCount) {
      sentiment = 'negative';
      confidence = (negativeCount / (positiveCount + negativeCount + 1)).clamp(0.0, 1.0);
    } else {
      sentiment = 'neutral';
      confidence = 0.5;
    }

    return SentimentResult(
      sentiment: sentiment,
      confidence: confidence,
      keywords: foundKeywords.take(5).toList(),
    );
  }

  // Extract emotions from text
  List<EmotionResult> extractEmotions(String text) {
    final emotionKeywords = {
      'joy': ['happy', 'joy', 'excited', 'cheerful', 'delighted', 'elated'],
      'sadness': ['sad', 'depressed', 'melancholy', 'gloomy', 'sorrowful'],
      'anger': ['angry', 'furious', 'irritated', 'annoyed', 'frustrated'],
      'fear': ['afraid', 'scared', 'anxious', 'worried', 'nervous', 'terrified'],
      'surprise': ['surprised', 'amazed', 'astonished', 'shocked', 'stunned'],
      'disgust': ['disgusted', 'revolted', 'repulsed', 'sickened'],
      'trust': ['trust', 'confident', 'secure', 'safe', 'reliable'],
      'anticipation': ['excited', 'eager', 'hopeful', 'optimistic', 'expectant'],
    };

    final words = text.toLowerCase().split(RegExp(r'\W+'));
    Map<String, int> emotionCounts = {};

    for (String word in words) {
      for (String emotion in emotionKeywords.keys) {
        if (emotionKeywords[emotion]!.contains(word)) {
          emotionCounts[emotion] = (emotionCounts[emotion] ?? 0) + 1;
        }
      }
    }

    List<EmotionResult> emotions = [];
    emotionCounts.forEach((emotion, count) {
      double intensity = (count / words.length * 10).clamp(0.0, 1.0);
      emotions.add(EmotionResult(
        emotion: emotion,
        intensity: intensity,
        keywords: emotionKeywords[emotion]!
            .where((keyword) => words.contains(keyword))
            .toList(),
      ));
    });

    // Sort by intensity
    emotions.sort((a, b) => b.intensity.compareTo(a.intensity));
    return emotions.take(3).toList(); // Return top 3 emotions
  }

  // Generate mood insights
  String generateMoodInsights(MoodAnalysisResult analysis) {
    List<String> insights = [];

    // Sentiment insights
    if (analysis.sentiment == 'positive') {
      insights.add('Your overall mood appears positive. Keep up the good energy!');
    } else if (analysis.sentiment == 'negative') {
      insights.add('You seem to be experiencing some challenges. Consider talking to someone you trust.');
    } else {
      insights.add('Your mood appears balanced. This is a good foundation for emotional well-being.');
    }

    // Confidence insights
    if (analysis.confidence > 0.8) {
      insights.add('The analysis shows strong emotional indicators in your text.');
    } else if (analysis.confidence < 0.4) {
      insights.add('Your emotional expression seems mixed or subtle.');
    }

    // Keyword insights
    if (analysis.keywords.isNotEmpty) {
      insights.add('Key emotional words detected: ${analysis.keywords.join(', ')}');
    }

    return insights.join(' ');
  }

  // Get mood color based on sentiment
  String getMoodColor(String sentiment) {
    switch (sentiment.toLowerCase()) {
      case 'positive':
        return '#4CAF50'; // Green
      case 'negative':
        return '#F44336'; // Red
      case 'neutral':
        return '#FF9800'; // Orange
      default:
        return '#9E9E9E'; // Grey
    }
  }

  // Get mood emoji based on sentiment
  String getMoodEmoji(String sentiment) {
    switch (sentiment.toLowerCase()) {
      case 'positive':
        return '😊';
      case 'negative':
        return '😔';
      case 'neutral':
        return '😐';
      default:
        return '🤔';
    }
  }
}

// Data models
class MoodAnalysisResult {
  final String sentiment;
  final double confidence;
  final List<String> keywords;
  final List<EmotionResult> emotions;
  final String insights;

  MoodAnalysisResult({
    required this.sentiment,
    required this.confidence,
    required this.keywords,
    required this.emotions,
    required this.insights,
  });

  factory MoodAnalysisResult.fromJson(Map<String, dynamic> json) {
    return MoodAnalysisResult(
      sentiment: json['sentiment'] ?? 'neutral',
      confidence: (json['confidence'] ?? 0.0).toDouble(),
      keywords: List<String>.from(json['keywords'] ?? []),
      emotions: (json['emotions'] as List<dynamic>?)
          ?.map((e) => EmotionResult.fromJson(e))
          .toList() ?? [],
      insights: json['insights'] ?? '',
    );
  }
}

class JournalAnalysisResult {
  final String overallMood;
  final double moodScore;
  final List<String> themes;
  final List<String> suggestions;
  final Map<String, dynamic> trends;

  JournalAnalysisResult({
    required this.overallMood,
    required this.moodScore,
    required this.themes,
    required this.suggestions,
    required this.trends,
  });

  factory JournalAnalysisResult.fromJson(Map<String, dynamic> json) {
    return JournalAnalysisResult(
      overallMood: json['overallMood'] ?? 'neutral',
      moodScore: (json['moodScore'] ?? 0.0).toDouble(),
      themes: List<String>.from(json['themes'] ?? []),
      suggestions: List<String>.from(json['suggestions'] ?? []),
      trends: json['trends'] ?? {},
    );
  }
}

class MoodTrendsResult {
  final List<MoodDataPoint> dailyMoods;
  final Map<String, double> emotionAverages;
  final List<String> insights;
  final double overallTrend;

  MoodTrendsResult({
    required this.dailyMoods,
    required this.emotionAverages,
    required this.insights,
    required this.overallTrend,
  });

  factory MoodTrendsResult.fromJson(Map<String, dynamic> json) {
    return MoodTrendsResult(
      dailyMoods: (json['dailyMoods'] as List<dynamic>?)
          ?.map((e) => MoodDataPoint.fromJson(e))
          .toList() ?? [],
      emotionAverages: Map<String, double>.from(json['emotionAverages'] ?? {}),
      insights: List<String>.from(json['insights'] ?? []),
      overallTrend: (json['overallTrend'] ?? 0.0).toDouble(),
    );
  }
}

class EmotionResult {
  final String emotion;
  final double intensity;
  final List<String> keywords;

  EmotionResult({
    required this.emotion,
    required this.intensity,
    required this.keywords,
  });

  factory EmotionResult.fromJson(Map<String, dynamic> json) {
    return EmotionResult(
      emotion: json['emotion'] ?? '',
      intensity: (json['intensity'] ?? 0.0).toDouble(),
      keywords: List<String>.from(json['keywords'] ?? []),
    );
  }
}

class SentimentResult {
  final String sentiment;
  final double confidence;
  final List<String> keywords;

  SentimentResult({
    required this.sentiment,
    required this.confidence,
    required this.keywords,
  });
}

class MoodDataPoint {
  final DateTime date;
  final String mood;
  final double score;

  MoodDataPoint({
    required this.date,
    required this.mood,
    required this.score,
  });

  factory MoodDataPoint.fromJson(Map<String, dynamic> json) {
    return MoodDataPoint(
      date: DateTime.parse(json['date']),
      mood: json['mood'] ?? 'neutral',
      score: (json['score'] ?? 0.0).toDouble(),
    );
  }
}
